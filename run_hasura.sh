#!/bin/bash
set -xeuo pipefail

# Get the absolute path of the directory where this script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

export LD_LIBRARY_PATH="/app/.apt/lib/x86_64-linux-gnu/:${SCRIPT_DIR}/.apt/lib/x86_64-linux-gnu/:${LD_LIBRARY_PATH:-}"
export HASURA_GRAPHQL_METADATA_DATABASE_URL="${DATABASE_URL}"
export HASURA_GRAPHQL_PG_CONNECTIONS=15
export HASURA_GRAPHQL_ENABLE_CONSOLE=true
export HASURA_GRAPHQL_SKIP_UPDATE_CHECK=true
export HASURA_GRAPHQL_DISABLE_INTERACTIVE=true
export HASURA_GRAPHQL_ENABLE_TELEMETRY=false
export HASURA_GRAPHQL_METADATA_DATABASE_EXTENSIONS_SCHEMA="${HASURA_GRAPHQL_METADATA_DATABASE_EXTENSIONS_SCHEMA:-heroku_ext}"
export HASURA_PORT="${HASURA_PORT:-3020}"
export HASURA_GRAPHQL_JWT_SECRET='{"type": "HS256", "key": "'"${JWT_SECRET_KEY}"'"}'

if [ "${#JWT_SECRET_KEY}" -lt 10 ]; then
  echo "10 character secret key minimum" >&2
  exit 1
fi

HEALTH_CHECK_URL="http://localhost:${HASURA_PORT}/healthz"

if [ "$APP_TYPE" == "test" ] || [ "$APP_TYPE" == "staging" ]; then
	export HASURA_GRAPHQL_DEV_MODE=true 
else
	export HASURA_GRAPHQL_DEV_MODE=false
fi

cd "${SCRIPT_DIR}/hasura/"

echo "Hasura version: $(./hasura-engine version)"

if [ "${RUN_HASURA_BACKGROUND:-false}" == "true" ]; then
	./hasura-engine serve --server-port "${HASURA_PORT}" &
	export GRAPHQL_PID=$!

	echo "Waiting for GraphQL Engine to be reachable at ${HEALTH_CHECK_URL} (PID: ${GRAPHQL_PID})..."
	SECONDS_WAITED=0
	TIMEOUT_SECONDS=60 
	SLEEP_INTERVAL=1 

	while true; do
	  if ! kill -0 "$GRAPHQL_PID" 2>/dev/null; then
	    echo "Error: GraphQL Engine process died unexpectedly."
	    exit 1
	  fi

	  if curl --silent --fail --max-time "${SLEEP_INTERVAL}" --output /dev/null "${HEALTH_CHECK_URL}"; then
	    echo "GraphQL Engine is reachable on port ${HASURA_PORT}!"
	    echo "PID: ${GRAPHQL_PID}"
	    break
	  fi

	  SECONDS_WAITED=$((SECONDS_WAITED + SLEEP_INTERVAL))

	  if [ "$SECONDS_WAITED" -ge "$TIMEOUT_SECONDS" ]; then
	    echo "Timeout: GraphQL Engine did not become healthy after ${TIMEOUT_SECONDS} seconds."
	    "${SCRIPT_DIR}/wait_for_hasura_to_die.sh" "${GRAPHQL_PID}"
	    exit 1
	  fi

	  echo "Not ready yet (waited ${SECONDS_WAITED}s). Retrying in ${SLEEP_INTERVAL} seconds..."
	  sleep "$SLEEP_INTERVAL"
	done
else
	echo "Running hasura engine in foreground"
	./hasura-engine serve --server-port "${HASURA_PORT}"
	export GRAPHQL_PID=""
fi
