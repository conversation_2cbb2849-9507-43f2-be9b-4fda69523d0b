#!/bin/bash
set -xeuo pipefail

# Get the absolute path of the directory where this script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

# The tricky part of this is getting a hasura instance with the 
# latest configuration, but without changing the main database.

# All these are taken from our binary repository.
# How to update these is described in the readme.md in that BINARY_RESPOSITORY.
HASURA_CLI_FILENAME="cli-linux-amd64-v2.47.0" 
HASURA_ENGINE_FILENAME="graphql-engine-linux-amd64-v2.47.0"
BACKUP_FILENAME="rezibase-staging-21-May-25.dump" 

BINARY_REPOSITORY="https://raw.githubusercontent.com/trevorHansenCB/rezibase-app-binaries/main/"

if [ -z "$GITHUB_BINARY_REPO_TOKEN" ]; then
  echo "ERROR: GITHUB_BINARY_REPO_TOKEN environment variable is not set."
  exit 1
fi

export HASURA_GRAPHQL_ENDPOINT="http://localhost:3020"
export HASURA_ADMIN_URL=${HASURA_GRAPHQL_ENDPOINT}
export VITE_APP_HASURA_URL="${HASURA_GRAPHQL_ENDPOINT}/v1/graphql"

if [ "$APP_TYPE" == "test" ]; then
  echo "-----> Detected APP_TYPE = test."
  export BUILD_DATABASE_URL=${DATABASE_URL}
fi

if [ -z "$BUILD_DATABASE_URL" ]; then
  echo "ERROR: BUILD_DATABASE_URL environment variable is not set."
  exit 1
fi

if [ "$APP_TYPE" == "test" ] || [ "$APP_TYPE" == "staging" ]; then
  # If we set npm_config_production to false, the slug is >500MB
  # and can't be pushed.
  echo "test or staging detected"
else
  export NPM_CONFIG_PRODUCTION=${NPM_CONFIG_PRODUCTION:-true}
fi

# Flask uses config.py which uses the DATABASE_URL..
# Hasura is configured to read from this env variable,
# So we overwrite it so that hasura will connect to the upgraded instance instead.
export DATABASE_URL=${BUILD_DATABASE_URL}

GRAPHQL_PID=""

# --- Cleanup Function ---
# This function will be called on script exit to ensure the GraphQL engine is stopped.
cleanup_graphql_engine() {
  "${SCRIPT_DIR}/wait_for_hasura_to_die.sh" "$GRAPHQL_PID"
}

# --- Trap Setup ---
# Call cleanup_graphql_engine when the script exits for any reason.
trap cleanup_graphql_engine EXIT

# Function to download a file with GitHub token authentication
download_file() {
  local output_file="$1"
  local file_url="${BINARY_REPOSITORY}${output_file}"
  local http_status_code

  if [ -z "$output_file" ]; then
    echo "ERROR (download_file): Output file path is required." >&2
    exit 1
  fi

  echo "INFO: Attempting to download file from ${file_url} to ${output_file}..."

  # Perform the download and capture HTTP status code
  # Uses GITHUB_BINARY_REPO_TOKEN from the environment
  http_status_code=$(curl -s -L \
    -H "Authorization: token $GITHUB_BINARY_REPO_TOKEN" \
    -H "Accept: application/octet-stream" \
    -o "$output_file" \
    -w "%{http_code}" \
    "$file_url")

  # Check if curl itself failed (e.g., network error before HTTP response)
  if [ $? -ne 0 ] && [ "$http_status_code" == "000" ]; then
      echo "ERROR: curl command failed to execute or could not connect. (Is the URL correct and network up?)" >&2
      rm -f "$output_file"
      exit 1
  fi

  # Check HTTP status code
  if [ "$http_status_code" -ne 200 ]; then
      echo "ERROR: Failed to download file. HTTP Status: $http_status_code" >&2
      rm -f "$output_file"
      exit 1
  fi

  echo "INFO: Download reported HTTP status 200."

  # Check if the file was actually created and is not empty
  if [ ! -s "$output_file" ]; then
    echo "ERROR: Download successful (HTTP 200) but $output_file is empty or not found." >&2
    rm -f "$output_file"
    exit 1
  fi

  echo "INFO: file $output_file downloaded successfully and is not empty."
  return 0 # Success
}

(
  mkdir -p "hasura"
  cd "hasura"

  download_file "$HASURA_CLI_FILENAME"
  mv "$HASURA_CLI_FILENAME" "./hasura-cli"
  chmod +x "./hasura-cli"

  download_file "$HASURA_ENGINE_FILENAME"
  mv "$HASURA_ENGINE_FILENAME" "./hasura-engine"
  chmod +x "./hasura-engine"
)

echo "-----> Attempting restore of database from $BACKUP_FILENAME to $BUILD_DATABASE_URL..."

#We don't want to accidentally be connected to production/staging.
#So check that the table count is zero (fresh deploy) or,
#it contains a cardiobase_testing table, which we create later, if we're rebuilding.

USER_TABLE_COUNT=$(psql -d "$BUILD_DATABASE_URL" -tAc "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema NOT IN ('pg_catalog', 'information_schema') AND table_type = 'BASE TABLE';")
TABLE_EXISTS_COUNT=$(psql -d "$BUILD_DATABASE_URL" -tAc "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'cardiobase_testing' AND table_type = 'BASE TABLE';")

if [[ "$USER_TABLE_COUNT" -gt  0 && "$TABLE_EXISTS_COUNT" -eq  0 ]]; then
  echo "ERROR: Build database is not empty. Found $USER_TABLE_COUNT user table(s), but no 'cardiobase_testing' table. It seems like we're connected to the wrong database. Be careful! Aborting script." >&2
  exit 1
fi

run_build_sql() {
  local sql_command="$1"

  # --- Execute psql Command ---
  if psql -X -v -d "$BUILD_DATABASE_URL" -c "$sql_command"; then
    return 0 # Success
  else
    echo "ERROR (run_sql): " >&2
    echo "  Database URL used: $BUILD_DATABASE_URL" >&2
    echo "  Command attempted: $sql_command" >&2
    return 1 # Failure
  fi
}

# pg_restore --clean, will delete just the stuff that it's in the backup.
# So we want to clean out everything, for example if there are tables introduced by previous upgrades.
if [ "$TABLE_EXISTS_COUNT" -gt 0 ]; then      
  run_build_sql "DROP SCHEMA IF EXISTS hdb_catalog CASCADE;"
  run_build_sql "DROP SCHEMA IF EXISTS public CASCADE;"
  run_build_sql "DROP SCHEMA IF EXISTS heroku_ext CASCADE;"

  run_build_sql "CREATE SCHEMA heroku_ext;"
  run_build_sql "CREATE SCHEMA public;"
  run_build_sql "CREATE SCHEMA hdb_catalog;"
fi

download_file "$BACKUP_FILENAME"

# We got some errors on restore because the server we're using is managed and doesn't 
# let us configure some global values. So ignore with || true.
pg_restore --version
pg_restore --clean --if-exists --no-acl --no-owner -d "$BUILD_DATABASE_URL" "$BACKUP_FILENAME" || true

run_build_sql "CREATE TABLE IF NOT EXISTS cardiobase_testing (data_field TEXT)"

rm "$BACKUP_FILENAME"
echo "-----> Database restore complete."

export RUN_HASURA_BACKGROUND=true
source "${SCRIPT_DIR}/run_hasura.sh"

cd "${SCRIPT_DIR}/hasura/"

"${SCRIPT_DIR}/run_update_hasura.sh"

cd "../FE"
npm install --dev
npm run build
npx vitest
npm prune --production

cleanup_graphql_engine
