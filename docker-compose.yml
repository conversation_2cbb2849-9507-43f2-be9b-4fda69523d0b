services:

  db:
    image: postgres:latest
    restart: always
    env_file:
      - .env
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - './postgres_data:/data'
    ports:
      - "5432:5432"

  web:
    platform: linux/amd64
    env_file:
      - .env
    build: .
    volumes:
      - './:/rezibase-qc-cloud'
    ports:
      - "${FLASK_PORT}:${FLASK_PORT}"
    depends_on:
      - db

  graphql-engine:
    image: hasura/graphql-engine:v2.46.0
    ports:
      - "8080:8080"
    restart: always
    env_file:
      - .env
    depends_on:
      data-connector-agent:
        condition: service_healthy

  data-connector-agent:
    image: hasura/graphql-data-connector:v2.46.0
    restart: always
    ports:
      - 8081:8081
    environment:
      QUARKUS_LOG_LEVEL: ERROR
      QUARKUS_OPENTELEMETRY_ENABLED: "false"
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8081/api/v1/athena/health" ]
      interval: 5s
      timeout: 10s
      retries: 5
      start_period: 5s

volumes:
  postgres_data:
