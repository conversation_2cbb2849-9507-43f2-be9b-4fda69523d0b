{"buildpacks": [{"url": "https://buildpack-registry.s3.amazonaws.com/buildpacks/heroku-community/apt.tgz"}, {"url": "https://github.com/clinibase/heroku-buildpack-traefik"}, {"url": "heroku/python"}, {"url": "hero<PERSON>/nodejs"}], "addons": [{"plan": "heroku-postgresql:essential-0", "as": "DATABASE"}], "scripts": {"test": "./run_tests.sh"}, "formation": {"web": {"quantity": 1, "size": "standard-2x"}}}