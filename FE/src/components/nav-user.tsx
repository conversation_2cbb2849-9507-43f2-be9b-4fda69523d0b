import {<PERSON>, useNavigate} from 'react-router';

import {Check, CircleUser, LogOut, Phone} from 'lucide-react';
import {observer} from 'mobx-react-lite';

import {Site} from '@/api-types/schema.ts';
import {apolloClient} from '@/apollo-client.ts';
import {Avatar, AvatarFallback} from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {queryClient} from '@/query-client.ts';
import authStore from '@/store/auth.store.ts';

import {Paths} from '../api-types/routePaths.ts';
import {useApiQuery} from '../hooks/use-api-query.ts';

function getInitials(name?: string) {
  if (!name) return undefined;

  const nameSplit = name.toString().toUpperCase().split(' ').filter(Boolean);
  if (nameSplit.length === 1) {
    return nameSplit[0].slice(0, 2);
  }
  return nameSplit
    .slice(0, 2)
    .map((e) => e[0])
    .join('');
}

export const NavUser = observer(() => {
  const navigate = useNavigate();
  const {data: currentUser} = useApiQuery(Paths.PROFILE);
  const {data: sitesList} = useApiQuery(Paths.SITES_LIST);

  function logout() {
    authStore.logout();
    queryClient.clear();
    apolloClient.resetStore();
    localStorage.removeItem('rezibase:last_visited');
    navigate('/auth/login');
  }

  const site = sitesList?.find?.((site: Site) => site?.value === authStore.tokenSiteId);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground outline-hidden data-[state=open]:rounded-lg">
        <div className="flex items-center gap-2 pl-0.5">
          <Avatar className="h-9 w-9 rounded-full">
            <AvatarFallback className="bg-brand2-400 rounded-lg text-sm text-white">
              {getInitials(currentUser?.name ?? currentUser?.email)}
            </AvatarFallback>
          </Avatar>
          <div className="flex flex-col items-start justify-center text-neutral-700">
            <span className="text-xs font-semibold">{currentUser?.name}</span>
            <span className="m text-xs">{site?.text}</span>
          </div>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
        side="bottom"
        align="end"
        sideOffset={4}
      >
        <DropdownMenuGroup>
          <DropdownMenuLabel className="text-xs text-neutral-700">Sites</DropdownMenuLabel>
          {sitesList?.map?.((site: any) => (
            <DropdownMenuItem
              onClick={
                async () => {
                  try {
                    await authStore.switchSite(site.value);
                    await apolloClient.resetStore();
                    await queryClient.clear();

                    localStorage.removeItem('rezibase:last_visited');
                    navigate('/');
                  } catch (error) {
                    console.error('Site switch failed:', error);
                  }
                }
              }
              key={site.value}
              className="text-brand-800 flex cursor-pointer items-center"
            >
              {authStore.tokenSiteId == site?.value ? <Check className="text-brand2-600" /> : <svg />}
              {site.text}
            </DropdownMenuItem>
          ))}
        </DropdownMenuGroup>

        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={() =>
            (window.location.href = (window.globalEnv.VITE_APP_ADMIN_URL as string) ?? '/admin/')
          }
          className="text-brand-800 cursor-pointer"
        >
          <CircleUser />
          Admin
        </DropdownMenuItem>
        <Link
          target="__blank"
          to="https://cardiobase.com/contactsupport/"
        >
          <DropdownMenuItem className="text-brand-800 cursor-pointer">
            <Phone />
            Contact
          </DropdownMenuItem>
        </Link>
        <DropdownMenuItem
          onClick={logout}
          className="text-brand-800 cursor-pointer"
        >
          <LogOut />
          Log out
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
});
