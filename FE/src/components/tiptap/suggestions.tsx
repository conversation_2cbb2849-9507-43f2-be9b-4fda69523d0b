import clsx from 'clsx';
import { useCallback, useEffect, useRef, useState } from 'react';
import { Editor } from '@tiptap/react';
import Fuse from 'fuse.js';
import { Popover, ListBox, ListBoxItem } from 'react-aria-components';

interface SuggestionsProps {
  suggestions: string[];
  editor: Editor | null;
  className?: string;
}

const fuseOptions = {
  includeScore: true,
  threshold: 0.3,
  keys: ['text'],
};

export const Suggestions = ({ suggestions, editor, className }: SuggestionsProps) => {
  const [suggestionList, setSuggestionList] = useState<string[]>([]);
  const [suggestionPosition, setSuggestionPosition] = useState<{
    top: number;
    left: number;
  } | null>(null);
  const [selectedIndex, setSelectedIndex] = useState<number>(0);
  const lastValidSuggestionsRef = useRef<string[]>([]);
  const triggerRef = useRef<HTMLDivElement>(null);
  const editorRef = useRef<HTMLElement | null>(null);
  const popoverRef = useRef<HTMLDivElement>(null);

  const suggestionObjects = suggestions.map((text) => ({ text }));
  const fuse = new Fuse(suggestionObjects, fuseOptions);

  const applySuggestion = useCallback(
    (suggestion: string) => {
      if (!editor) return;

      const { from } = editor.state.selection;
      const textBeforeCursor = editor.state.doc.textBetween(0, from, ' ');
      const words = textBeforeCursor.split(/\s+/).filter((w) => w.length > 0);
      const currentWord = words[words.length - 1] || '';
      const wordStart = textBeforeCursor.lastIndexOf(currentWord);

      editor
        .chain()
        .deleteRange({ from: wordStart, to: from })
        .insertContent(' ' +suggestion + '')
        .focus()
        .run();

      setSuggestionList([]);
      lastValidSuggestionsRef.current = [];
      setSuggestionPosition(null);
      setSelectedIndex(0);
    },
    [editor]
  );

  useEffect(() => {
    if (!editor) {
      return undefined;
    }

    editorRef.current = (editor.view.dom.closest('.tiptap') as HTMLElement | null) || editor.view.dom;

    const handleUpdate = () => {
      const { from } = editor.state.selection;
      const textBeforeCursor = editor.state.doc.textBetween(0, from, ' ');
      const words = textBeforeCursor.split(/\s+/).filter((w) => w.length > 0);
      const currentWord = words[words.length - 1] || '';

      if (currentWord.length > 0) {
        const searchResults = fuse.search(currentWord);
        const filteredSuggestions = searchResults
          .filter((result) => result.item.text.toLowerCase().startsWith(currentWord.toLowerCase()))
          .slice(0, 5)
          .map((result) => result.item.text);

        setSuggestionList(filteredSuggestions);
        lastValidSuggestionsRef.current = filteredSuggestions;

        const coords = editor.view.coordsAtPos(from);
        const editorRect = editorRef.current?.getBoundingClientRect();

        if (editorRect) {
          setSuggestionPosition({
            top: coords.bottom - editorRect.top,
            left: coords.left - editorRect.left,
          });
        } else {
          console.warn('Editor bounding rect not available');
          setSuggestionPosition({
            top: coords.bottom,
            left: coords.left,
          });
        }
      } else {
        setSuggestionList([]);
        setSuggestionPosition(null);
      }
    };

    editor.on('update', handleUpdate);
    return () => {
      editor.off('update', handleUpdate);
    };
  }, [editor]);

  useEffect(() => {
    if (!editor) return undefined;

    const handleKeyDown = (event: KeyboardEvent) => {
      const currentSuggestions = lastValidSuggestionsRef.current;
      if (!currentSuggestions.length) return;

      switch (event.key) {
        case 'ArrowDown':
          setSelectedIndex((prev) => (prev + 1) % currentSuggestions.length);
          event.preventDefault();
          break;
        case 'ArrowUp':
          setSelectedIndex((prev) => (prev - 1 < 0 ? currentSuggestions.length - 1 : prev - 1));
          event.preventDefault();
          break;
        case 'Enter':
        case 'Tab':
          applySuggestion(currentSuggestions[selectedIndex].trim());
          event.preventDefault();
          event.stopPropagation();
          break;
        case 'Escape':
          setSuggestionList([]);
          lastValidSuggestionsRef.current = [];
          setSuggestionPosition(null);
          event.preventDefault();
          break;
        default:
          return;
      }
    };

    const editorDom = editor.view.dom;
    editorDom.addEventListener('keydown', handleKeyDown);
    return () => {
      editorDom.removeEventListener('keydown', handleKeyDown);
    };
  }, [editor, applySuggestion, selectedIndex]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        popoverRef.current &&
        !popoverRef.current.contains(event.target as Node) &&
        triggerRef.current &&
        !triggerRef.current.contains(event.target as Node)
      ) {
        setSuggestionList([]);
        lastValidSuggestionsRef.current = [];
        setSuggestionPosition(null);
        setSelectedIndex(0);
      }
    };

    if (suggestionList.length > 0) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [suggestionList]);

  if (!editor) return null;

  return (
    <>
      <div
        ref={triggerRef}
        className="absolute pointer-events-none"
        style={{
          top: suggestionPosition?.top ?? 0,
          left: suggestionPosition?.left ?? 0,
          width: 1,
          height: 1,
        }}
      />
      {suggestionPosition && suggestionList.length > 0 && (
        <Popover
          ref={popoverRef}
          triggerRef={triggerRef}
          isOpen={true}
          placement="bottom start"
          offset={2}
          shouldFlip={true}
          isNonModal={true}
          className={clsx(
            'react-aria-Popover max-h-56 w-64 overflow-y-auto rounded border bg-white shadow-lg',
            className
          )}
        >
          <ListBox
            aria-label="Suggestions"
            items={suggestionList.map((suggestion, index) => ({
              id: index,
              text: suggestion,
            }))}
            selectionMode="single"
            selectedKeys={[selectedIndex]}
            onSelectionChange={(keys) => {
              const newIndex = Array.from(keys)[0] ? Number(Array.from(keys)[0]) : 0;
              setSelectedIndex(newIndex);
              applySuggestion(suggestionList[newIndex]);
            }}
            shouldFocusWrap={false}
          >
            {(item) => (
              <ListBoxItem
                id={item.id}
                className={({ isSelected, isFocused }) =>
                  clsx(
                    'cursor-pointer p-1 hover:bg-gray-100',
                    isFocused && 'bg-gray-100',
                    isSelected && 'bg-gray-200'
                  )
                }
                onAction={() => applySuggestion(item.text)}
              >
                {item.text}
              </ListBoxItem>
            )}
          </ListBox>
        </Popover>
      )}
    </>
  );
};