import authStore from '@/store/auth.store.ts';

import {RftStore} from '../studies-rft/store/rtf.store.ts';
import {AutoInterpret_rft_ATSERS2021} from './ats-ers-2021.ts';

export default function autoInterpretRft(
  rftStore: RftStore,
  notify?: (msg: string) => void
) {
  const autoReportAlgorithm = authStore.site?.getConfig('autoreport_algorithm') ?? 'ats_ers_2021';

  if (autoReportAlgorithm === 'ats_ers_2021') {
    return AutoInterpret_rft_ATSERS2021(rftStore, notify).replaceAll(/\s+/g, ' ');
  }
}
