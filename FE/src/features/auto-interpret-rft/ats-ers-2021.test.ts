import { describe, it, expect, vi } from 'vitest';
import { AutoInterpret_rft_ATSERS2021 } from './ats-ers-2021';

// Mock alert function for the testing environment
global.alert = vi.fn();

describe('AutoInterpret_rft_ATSERS2021', () => {
  interface ParamWithValueAndLln {
    result: number;
    lln: number;
  }

  interface Fev1Params extends ParamWithValueAndLln {
    zScore: number;
  }

  interface SpiroMockParams {
    fev1: Fev1Params;
    fvc: ParamWithValueAndLln;
    vc?: ParamWithValueAndLln; // Optional, defaults to fvc if not provided
    ferLln: number;
  }

  function createMockRftStore({ fev1, fvc, vc: vcInput, ferLln }: SpiroMockParams) {
    const vc = vcInput ?? fvc; // Default VC to FVC values if not provided

    return {
      spirometryStore: {
        getSp1Value: vi.fn((param: string) => {
          switch (param) {
            case 'FEV1':
              return { result: fev1.result, predResult: { lln: fev1.lln }, zScore: fev1.zScore };
            case 'FVC':
              return { result: fvc.result, predResult: { lln: fvc.lln } };
            case 'VC':
              return { result: vc.result, predResult: { lln: vc.lln } };
            case 'FER': // FEV1/FVC ratio.
              return { result: fev1.result / fvc.result, predResult: { lln: ferLln } };
            default:
              return { result: 0, predResult: { lln: 0 }, zScore: 0 };
          }
        }),
        getSp2Value: vi.fn(() => ({ result: undefined })), // No post-BD data
        sp1Condition: 'Baseline',
        sp2Condition: null,
      },
      coTransferStore: {
        getParamValue: vi.fn(() => ({ result: 0, predResult: { lln: 0, uln: 0 }, zScore: 0 })),
        hbFactor: 1,
      },
      lungVolumesStore: {
        getParamValue: vi.fn(() => ({ result: 0, predResult: { lln: 0, uln: 0 }, zScore: 0 })),
      },
      mrpsStore: {
        getParamValue: vi.fn(() => ({ result: 0, predResult: { lln: 0 } })),
      },
      exhaledNitricOxideStore: {
        getParamValue: vi.fn(() => ({ result: 0, predResult: { uln: 0 } })),
      },
      bloodGasesStore: {
        getResult1Value: vi.fn(() => ({ result: 0, predResult: { lln: 0, uln: 0 } })),
        result1: { fio2: 'air' },
      },
    };
  }

  it('should report "is within normal limits" when FEV1 is 3.00 and other spirometry values define a normal pattern', () => {
    const mockRftStore = createMockRftStore({
      fev1: { result: 3.00, lln: 2.50, zScore: 0.5 },
      fvc: { result: 4.00, lln: 3.50 },
      ferLln: 0.70,
    });

    const reportText = AutoInterpret_rft_ATSERS2021(mockRftStore as any);
    expect(reportText).toContain('is within normal limits');
  });

  it('should report "mild restrictive defect" when FEV1=2.5, FVC=2.7, FER is normal, and FVC is below LLN with mild z-score', () => {
    const mockRftStore = createMockRftStore({
      fev1: { result: 2.5, lln: 2.8, zScore: -2.0 },
      fvc: { result: 2.7, lln: 3.0 },
      ferLln: 0.70,
    });

    const reportText = AutoInterpret_rft_ATSERS2021(mockRftStore as any);
    expect(reportText).toContain('mild restrictive defect');
  });
});