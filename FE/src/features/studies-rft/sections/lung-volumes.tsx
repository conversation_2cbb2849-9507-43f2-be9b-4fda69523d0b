import {useEffect} from 'react';
import {<PERSON>ton, ListBox, ListBoxItem, Popover, Select, SelectValue} from 'react-aria-components';

import {useQuery} from '@apollo/client';
import {ChevronDown} from 'lucide-react';
import {observer} from 'mobx-react-lite';

import NIL from '@/components/NIL.tsx';
import {DataSheet, DataSheetCell, DataSheetRow} from '@/components/data-sheet';
import {
  ParameterZScoreRange,
  ZScorePlotParamDot,
  ZScorePlotParamRow,
} from '@/features/studies-rft/ZScorePlot.tsx';
import {RftStore} from '@/features/studies-rft/store/rtf.store.ts';
import {getPreferenceField} from '@/graphql/preferences.ts';
import {CommitableParameterField} from "@/features/studies-rft/components/CommitableParameterField.tsx";

export const LungVolumesSection = observer(({rftStore}: {rftStore: RftStore}) => {
  const {data: testConditionPreference} = useQuery(getPreferenceField, {
    variables: {fieldName: 'Test condition'},
  });
  const {data: LVMethods} = useQuery(getPreferenceField, {variables: {fieldName: 'LungVolumeMethods'}});

  const lungVolumesStore = rftStore.lungVolumesStore;

  useEffect(() => {
    if (!testConditionPreference?.prefs_fields[0] || lungVolumesStore.condition) return;
    const defaultItem = testConditionPreference?.prefs_fields[0].prefs_fielditems.find(
      (e) => e.prefs_id === testConditionPreference?.prefs_fields[0].default_fielditem_id
    );

    lungVolumesStore.setProperty('condition', defaultItem?.fielditem ?? null);
  }, [testConditionPreference]);

  useEffect(() => {
    if (!LVMethods?.prefs_fields[0]) return;

    const lvMethods = LVMethods?.prefs_fields[0].prefs_fielditems;
    const defaultLvMethodId = LVMethods?.prefs_fields[0].default_fielditem_id;
    const defaultLvMethod = lvMethods.find((m) => m.prefs_id === defaultLvMethodId);

    if (defaultLvMethod?.fielditem) {
      lungVolumesStore.setProperty('method', defaultLvMethod?.fielditem);
    }
  }, [LVMethods]);

  return (
    <div>
      <div className="flex items-start justify-between gap-x-2">
        <div className="flex items-start gap-x-2">
          <div className="mt-0 w-32">
            <div className="flex h-[calc(var(--row-height)+4px)] items-center justify-end text-[10px] font-semibold text-neutral-800 uppercase">
              lung Volumes
            </div>
            <div className="h-[calc(var(--row-height)+4px)]" />
            {lungVolumesStore.baseline.values.map((value, index) => (
              <div
                key={index}
                className="flex h-(--row-height) items-center justify-end"
              >
                <div className="truncate text-right text-(length:--font-size) font-semibold text-neutral-600">
                  {value.parameter?.description ?? ''}{' '}
                  {value.parameter?.configAwareUnits && <>[{value.parameter?.configAwareUnits}]</>}
                </div>
              </div>
            ))}
          </div>

          <div>
            <Select
              selectedKey={lungVolumesStore.condition}
              onSelectionChange={(newValue) => {
                lungVolumesStore.setProperty('condition', newValue as string);
                lungVolumesStore.upsertDB('r_condition_lv');
              }}
              aria-label="Lung Volume test condition"
              isDisabled={!rftStore.isEditing}
            >
              <Button className="react-aria-Button -mb-0.25 h-[calc(var(--row-height)+4px)] w-full rounded-none rounded-t-sm px-3 py-1">
                <SelectValue className="react-aria-SelectValue text-[9px]/[1.3]" />
                <ChevronDown
                  className="size-3 text-gray-400"
                  aria-hidden="true"
                />
              </Button>
              <Popover>
                <ListBox items={testConditionPreference?.prefs_fields[0]?.prefs_fielditems ?? []}>
                  {(item) => <ListBoxItem id={item.fielditem?.toString()}>{item.fielditem}</ListBoxItem>}
                </ListBox>
              </Popover>
            </Select>

            <Select
              selectedKey={lungVolumesStore.method}
              onSelectionChange={(newValue) => {
                lungVolumesStore.setProperty('method', newValue as string)
                lungVolumesStore.upsertDB('lungvolumes_method');
              }}
              aria-label="Lung volume test method"
              isDisabled={!rftStore.isEditing}
            >
              <Button className="react-aria-Button -mb-0.25 h-[calc(var(--row-height)+4px)] w-full rounded-none px-3 py-1">
                <SelectValue className="react-aria-SelectValue text-[9px]/[1.3]" />
                <ChevronDown
                  className="size-3 text-gray-400"
                  aria-hidden="true"
                />
              </Button>
              <Popover>
                <ListBox items={LVMethods?.prefs_fields[0]?.prefs_fielditems ?? []}>
                  {(item) => <ListBoxItem id={item.fielditem?.toString()}>{item.fielditem}</ListBoxItem>}
                </ListBox>
              </Popover>
            </Select>

            <DataSheet className="grid-cols-[4.25rem_4.25rem_4.25rem_4.25rem]">
              {lungVolumesStore.baseline.values.map((value, index) => (
                <DataSheetRow key={index}>
                  <DataSheetCell className="text-right italic">{value.refValue ?? <NIL />}</DataSheetCell>
                  <DataSheetCell className="text-right">
                    <CommitableParameterField paramValue={value} rftStore={rftStore} />
                  </DataSheetCell>
                  <DataSheetCell className="text-right">
                    {value.zscoreFormatted ? value.zscoreFormatted : <NIL />}
                  </DataSheetCell>
                  <DataSheetCell className="text-right">
                    {value.predPercent ? (
                      `${Math.round(value.predPercent)}%`
                    ) : (
                      <NIL />
                    )}
                  </DataSheetCell>
                </DataSheetRow>
              ))}
            </DataSheet>
          </div>
        </div>

        <div id="lv-zscore-plot" className="mt-6 flex max-w-70 flex-1 shrink-0 items-center gap-x-2">
          <div className="w-full">
            <div className="text-[10px]/[1.3] font-medium text-neutral-600">
              <div className="mb-1.5 flex items-center justify-end gap-x-2">
                <div className="flex items-center gap-x-1">
                  <div className="bg-chart-1 size-2 rounded" />
                  <div>{lungVolumesStore.condition ?? 'Baseline'}</div>
                </div>
              </div>
            </div>

            {lungVolumesStore.baseline.values.map((value) => {
              if (!value.refValue) return <div className="my-0.5 h-3.5" />;

              return (
                <ZScorePlotParamRow
                  key={value.parameter?.description}
                  className="my-0.5 overflow-hidden"
                >
                  <ParameterZScoreRange parameterValue={value} />
                  {value?.zScore && !isNaN(value?.zScore) && <ZScorePlotParamDot val={value?.zScore ?? 0}/>}
                </ZScorePlotParamRow>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
});
