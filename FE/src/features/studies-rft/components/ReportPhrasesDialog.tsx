import {useEffect, useRef, useState} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>b, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>} from 'react-aria-components';

import {X} from 'lucide-react';

import Menu from '@/assets/iconly/Menu.svg?react';
import {useDialogState} from '@/components/modal-state-provider.tsx';

interface ReportPhrasesDialogProps {
  data: {
    id: number | undefined;
    name: string;
    fieldItems: {prefs_id: number; fielditem: string | null}[] | undefined;
  }[];
  onPhraseSelection: (selectedText: string) => void;
}

function ReportPhrasesDialog({data, onPhraseSelection}: ReportPhrasesDialogProps) {
  const [isOpen, setIsOpen] = useDialogState('report-phrases-dialogue');
  const [selectedTab, setSelectedTab] = useState(data[0]?.id);
  const selectedPhrase = data.find((item) => item.id === selectedTab);

  const sortedFieldItems = selectedPhrase?.fieldItems
    ? [...selectedPhrase.fieldItems].sort((a, b) => {
      const fieldA = a?.fielditem?.toLowerCase() || '';
      const fieldB = b?.fielditem?.toLowerCase() || '';
      return fieldA.localeCompare(fieldB);
    })
    : [];

  const [movableDialogElement, setMovableDialogElement] = useState<HTMLElement | null>(null);
  const dragHandleRef = useRef<HTMLDivElement | null>(null);
  const dragStartInfoRef = useRef<{
    mouseX: number;
    mouseY: number;
    initialTransform: DOMMatrixReadOnly;
  } | null>(null);

  useEffect(() => {
    const dialogNode = movableDialogElement;
    const handleNode = dragHandleRef.current;

    if (!dialogNode || !handleNode) {
      return;
    }

    const pointerDownHandler = (e: PointerEvent) => {
      if (e.pointerType === 'mouse' && e.button !== 0) return;

      e.preventDefault();
      const style = window.getComputedStyle(dialogNode);
      const matrix = new DOMMatrixReadOnly(style.transform);
      dragStartInfoRef.current = {
        mouseX: e.clientX,
        mouseY: e.clientY,
        initialTransform: matrix,
      };

      handleNode.style.cursor = 'grabbing';
      document.body.style.userSelect = 'none';
      handleNode.style.touchAction = 'none';

      document.addEventListener('pointermove', pointerMoveHandler);
      document.addEventListener('pointerup', pointerUpHandler);
    };

    const pointerMoveHandler = (e: PointerEvent) => {
      if (!dragStartInfoRef.current) return;

      e.preventDefault();

      const {mouseX: startX, mouseY: startY, initialTransform} = dragStartInfoRef.current;

      const deltaX = e.clientX - startX;
      const deltaY = e.clientY - startY;

      const newTranslateX = initialTransform.m41 + deltaX;
      const newTranslateY = initialTransform.m42 + deltaY;

      dialogNode.style.transform = `translate3d(${newTranslateX}px, ${newTranslateY}px, 0)`;
    };

    const pointerUpHandler = () => {
      if (!dragStartInfoRef.current) return;

      dragStartInfoRef.current = null;

      handleNode.style.cursor = 'grab';
      document.body.style.userSelect = '';
      handleNode.style.touchAction = '';

      document.removeEventListener('pointermove', pointerMoveHandler);
      document.removeEventListener('pointerup', pointerUpHandler);
    };

    handleNode.addEventListener('pointerdown', pointerDownHandler);
    handleNode.style.cursor = 'grab';

    return () => {
      handleNode.removeEventListener('pointerdown', pointerDownHandler);
      document.removeEventListener('pointermove', pointerMoveHandler);
      document.removeEventListener('pointerup', pointerUpHandler);

      if (handleNode) {
        handleNode.style.cursor = '';
        handleNode.style.touchAction = '';
      }
      if (document.body.style.userSelect === 'none') {
        document.body.style.userSelect = '';
      }
    };
  }, [movableDialogElement]);

  return (
    <Popover
      isOpen={isOpen}
      className="h-0"
      placement="bottom end"
      onOpenChange={setIsOpen}
    >
      <Dialog className="outline-none">

        <div
          ref={setMovableDialogElement}
          id="testID"
          className="react-aria-Popover w-120 p-0 outline-none"
        >
          <div className="react-aria-Dialog">
            <div className="flex items-center justify-between px-4 py-2.5">
              <div className="flex flex-grow items-center gap-x-2">
                <div
                  ref={dragHandleRef}
                  className="flex items-center justify-center p-1"
                >
                  <Menu className="h-4 w-4" />
                </div>
                <Heading
                  slot="title"
                  className="text-xs font-semibold text-neutral-900"
                >
                  Select Phrases
                </Heading>
              </div>
              <RACButton
                className="cursor-pointer"
                slot="close"
                onPress={() => setIsOpen(false)}
              >
                <X className="h-4 w-4 text-neutral-600" />
              </RACButton>
            </div>

            <div className="border-t border-neutral-200">
              <div className="flex flex-col px-5 py-6">
                <Tabs
                  selectedKey={selectedTab}
                  onSelectionChange={(key) => setSelectedTab(Number(key))}
                  className="flex h-6 flex-col"
                >
                  <TabList className="mb-2 flex border-b">
                    {data.map((item) => (
                      <Tab
                        key={item.id}
                        id={item.id}
                        className="data-[selected]:border-brand-500 data-[selected]:text-brand-500 relative -my-px cursor-pointer px-3.5 pb-2 text-xs font-semibold text-neutral-600 hover:text-neutral-800 focus:outline-none data-[selected]:border-b-2"
                      >
                        {item.name}
                      </Tab>
                    ))}
                  </TabList>
                </Tabs>

                <div className="h-full pt-5">
                  {sortedFieldItems?.length === 0 && (
                    <div className="w-full text-center text-sm text-neutral-600 italic">
                      No Phrase for this preference.
                    </div>
                  )}

                  <div className="space-y-2 w-full">
                    {sortedFieldItems?.map((item) => (
                      <div
                        key={item.prefs_id}
                        onClick={() => onPhraseSelection(item.fielditem ?? '')}
                        className="w-full cursor-pointer rounded-sm border px-3.5 py-1.5 text-xs text-neutral-800 hover:bg-neutral-100 focus:outline-none break-words"
                      >
                        {item.fielditem}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Dialog>
    </Popover>
  );
}

export default ReportPhrasesDialog;
