import {Dialog, DialogTrigger, Popover, <PERSON><PERSON> as RACButton} from 'react-aria-components';

import {TooltipContent} from '@radix-ui/react-tooltip';
import {format} from 'date-fns';
import {ChevronDown} from 'lucide-react';
import {observer} from 'mobx-react-lite';

import BankCardIcon from '@/assets/iconly/BankCard.svg?react';
import CalendarIcon from '@/assets/iconly/Calendar.svg?react';
import CalendarTimeIcon from '@/assets/iconly/CalendarTime.svg?react';
import CallPhoneIcon from '@/assets/iconly/CallPhone.svg?react';
import EmailIcon from '@/assets/iconly/Email.svg?react';
import HomeIcon from '@/assets/iconly/Home.svg?react';
import ManWomanIcon from '@/assets/iconly/ManWoman.svg?react';
import TranslateLanguageIcon from '@/assets/iconly/TranslateLanguage.svg?react';
import UsersIcon from '@/assets/iconly/Users.svg?react';
import WavesIcon from '@/assets/iconly/Waves.svg?react';
import {Tooltip, TooltipTrigger} from '@/components/ui/tooltip.tsx';
import {RftSession} from '@/features/studies-rft/store/rft-session.store.ts';

const InfoBlock = ({
  title,
  items,
}: {
  title: string;
  items: {id: string | undefined; value: string | undefined; label: string; icon: React.ReactNode}[];
}) => {
  return (
    <div className="flex flex-col">
      <p className="mb-1 text-sm font-semibold text-neutral-800">{title}</p>
      {items.map((item) => (
        <div
          key={item.id}
          className="flex items-center gap-x-2 py-1"
        >
          <div>{item.icon}</div>
          <p className="shrink-0 text-xs text-neutral-700">{item.label}:</p>

          <Tooltip>
            <TooltipTrigger className="w-50 truncate text-left text-xs text-neutral-900">
              {item.value}
            </TooltipTrigger>
            <TooltipContent
              className={
                item.value && item.value.length < 36
                  ? 'hidden'
                  : 'bg-brand-400 max-w-md rounded-md px-2 py-1 text-xs whitespace-pre-wrap text-white'
              }
            >
              {item.value}
            </TooltipContent>
          </Tooltip>
        </div>
      ))}
    </div>
  );
};

export const ReportPatientInfo = observer(({testSession}: {testSession?: RftSession}) => {
  const patient = testSession?.patient;
  if (!patient) return null;

  const generalInfoConfig = [
    {
      id: patient.dob?.toString(),
      label: 'DOB',
      value: patient?.dob && format(patient?.dob as unknown as Date, 'dd MMM, yyyy'),
      icon: <CalendarIcon className="size-4.5 text-neutral-500" />,
    },
    {
      id: patient.gender_code?.toString(),
      label: 'Gender',
      value: patient.genderResolved?.description,
      icon: <ManWomanIcon className="size-4.5 text-neutral-500" />,
    },
    {
      id: patient.countryofbirth_code?.toString(),
      label: 'Country of Birth',
      value: patient.countryOfBirthResolved?.description,
      icon: <WavesIcon className="size-4.5 text-neutral-500" />,
    },
    {
      id: patient.preferredlanguage_code?.toString(),
      label: 'Language',
      value: patient.preferredLanguageResolved?.description,
      icon: <TranslateLanguageIcon className="size-4.5 text-neutral-500" />,
    },
    {
      id: patient.aboriginalstatus_code?.toString(),
      label: 'Aboriginal Status',
      value: patient.aboriginalStatusResolved?.description,
      icon: <BankCardIcon className="size-4.5 text-neutral-500" />,
    },
    {
      id: patient.medicare_no?.toString(),
      label: 'Medicare No',
      value: patient.medicare_no,
      icon: <BankCardIcon className="size-4.5 text-neutral-500" />,
    },
    {
      id: patient.medicare_expirydate?.toString(),
      label: 'Medicare Expiry',
      value: patient.medicare_expirydate,
      icon: <CalendarTimeIcon className="size-4.5 text-neutral-500" />,
    },
  ];

  const contactInfoConfig = [
    {
      id: patient.phone_home?.toString(),
      label: 'Phone',
      value: patient.phone_home,
      icon: <CallPhoneIcon className="size-4.5 text-neutral-500" />,
    },
    {
      id: patient.phone_work?.toString(),
      label: 'Phone',
      value: patient.phone_work,
      icon: <CallPhoneIcon className="size-4.5 text-neutral-500" />,
    },
    {
      id: patient.email?.toString(),
      label: 'Email',
      value: patient.email,
      icon: <EmailIcon className="size-4.5 text-neutral-500" />,
    },
    {
      id: patient.addresses?.[0]?.address_1?.toString(),
      label: 'Address',
      value: patient.addresses?.[0]?.address_1,
      icon: <HomeIcon className="size-4.5 text-neutral-500" />,
    },
    {
      id: patient.addresses?.[0]?.address_2?.toString(),
      label: 'Address',
      value: patient.addresses?.[0]?.address_2,
      icon: <HomeIcon className="size-4.5 text-neutral-500" />,
    },
    {
      id: patient.addresses?.[0]?.suburb?.toString(),
      label: 'Suburb',
      value: patient.addresses?.[0]?.suburb,
      icon: <HomeIcon className="size-4.5 text-neutral-500" />,
    },
    {
      id: patient.addresses?.[0]?.postcode?.toString(),
      label: 'Postcode',
      value: patient.addresses?.[0]?.postcode,
      icon: <HomeIcon className="size-4.5 text-neutral-500" />,
    },
  ];

  const rftInfoConfig = [
    {
      id: patient.gender_forrfts_code?.toString(),
      label: 'Sex for RFTs',
      value: patient.genderForRftResolved?.description,
      icon: <ManWomanIcon className="size-4.5 text-neutral-500" />,
    },
    {
      id: patient.race_forrfts_code?.toString(),
      label: 'Ethnicity for RFTs',
      value: patient.ethnicityForRftResolved?.description,
      icon: <UsersIcon className="size-4.5 text-neutral-500" />,
    },
  ];

  const patientAge = patient.ageToday?.toFixed(0);

  return (
    <div className="sticky top-0 isolate z-30 -mx-6 -mt-6 mb-6 border-b border-neutral-200 bg-white py-3">
      <div className="flex items-center gap-x-2 px-6">
        <DialogTrigger>
          <RACButton className="flex shrink-0 cursor-pointer items-center gap-x-2">
            <div className="flex items-center gap-x-2">
              <div className="flex items-center gap-x-2">
                <p className="text-sm font-bold text-neutral-800">{patient.fullName}</p>

              </div>
              <p className="text-sm text-neutral-700">{patient.MRNResolved}</p>
              {patient.isDeceased && (
                <div className="rounded-md bg-red-50 px-2 text-[12px] text-red-700">Deceased</div>
              )}
            </div>
            <ChevronDown className="h-4 w-4 text-neutral-600 transition-transform duration-300 [[data-pressed='true']_&]:rotate-180" />
          </RACButton>
          <Popover
            placement="bottom start"
            className="react-aria-Popover mt-3 -ml-4 w-282 p-4"
          >
            <Dialog>
              <div className="flex items-start gap-x-20">
                {/*MRN + Name*/}
                <div className="shrink-0">
                  <p className="text-lg font-bold text-neutral-800">{patient.fullName}</p>
                  <p className="text-sm text-neutral-700">{patient.MRNResolved}</p>
                </div>

                <div className="mt-1.5 flex gap-x-6">
                  {/*Patient Generic Info*/}
                  <InfoBlock
                    items={generalInfoConfig}
                    title="General Info"
                  />

                  {/*Patient Contact Info*/}
                  <InfoBlock
                    items={contactInfoConfig}
                    title="Contact Info"
                  />

                  {/*Patient RFT Info*/}
                  <InfoBlock
                    items={rftInfoConfig}
                    title="RFT Info"
                  />
                </div>
              </div>
            </Dialog>
          </Popover>
        </DialogTrigger>

        <div className="flex w-full items-center justify-end gap-x-4">
          <div className="flex items-center gap-x-1 py-1">
            <CalendarIcon className="size-4.5 text-neutral-500" />
            <p className="shrink-0 text-xs text-neutral-700">DOB:</p>
            <p className="text-left text-xs text-neutral-900">
              {`${patient?.dob && format(patient.dob as unknown as Date, 'dd MMM, yyyy')} ${patientAge && `(${patientAge} yr)`}`}
            </p>
          </div>

          <div className="flex items-center gap-x-1 py-1">
            <CallPhoneIcon className="size-4.5 text-neutral-500" />
            <p className="shrink-0 text-xs text-neutral-700">Home Phone:</p>
            <p className="text-left text-xs text-neutral-900">{patient.phone_home}</p>
          </div>

          <div className="flex items-center gap-x-1 py-1">
            <ManWomanIcon className="size-4.5 text-neutral-500" />
            <p className="shrink-0 text-xs text-neutral-700">Sex for RFTs:</p>
            <p className="text-left text-xs text-neutral-900">{patient.genderForRftResolved?.description}</p>
          </div>

          <div className="flex items-center gap-x-1 py-1">
            <UsersIcon className="size-4.5 text-neutral-500" />
            <p className="shrink-0 text-xs text-neutral-700">Ethnicity for RFTs:</p>
            <p className="text-left text-xs text-neutral-900">
              {patient.ethnicityForRftResolved?.description}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
});
