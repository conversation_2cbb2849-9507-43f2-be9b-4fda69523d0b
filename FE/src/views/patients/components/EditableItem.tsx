import clsx from 'clsx';
import {useEffect, useMemo, useRef, useState} from 'react';
import {
  DatePicker,
  Dialog,
  ListBox,
  ListBoxItem,
  Popover,
  Button as RACButton,
  Select,
  SelectValue,
} from 'react-aria-components';
import {useParams} from 'react-router';

import {DocumentNode, useApolloClient} from '@apollo/client';
import {fromDate, getLocalTimeZone, toCalendarDateTime} from '@internationalized/date';
import {FocusScope} from '@react-aria/focus';
import {format} from 'date-fns';
import {Calendar as CalenderIcon, Check, ChevronDown} from 'lucide-react';

import {CheckmarkIconly} from '@/components/icons/CheckmarkIconly.tsx';
import {CloseRemoveIconly} from '@/components/icons/CloseRemoveIconly.tsx';
import {EditSquareIconly} from '@/components/icons/EditSquareIconly.tsx';
import {Input, Label} from '@/components/ui/Field.tsx';
import {Calendar} from '@/components/ui/calendar.tsx';
import {DateTimeSelector} from '@/components/ui/date-time-selector.tsx';
import Skeleton from '@/components/ui/skeleton/skeleton.tsx';
import {Tooltip, TooltipContent, TooltipTrigger} from '@/components/ui/tooltip.tsx';
import {getPatientsDetailData} from '@/graphql/patients';
import {getDoctorsData} from '@/graphql/contacts';
import {useConfirmation} from '@/hooks/use-confirmation.tsx';

interface EditableItemProps {
  type?: 'text' | 'number' | 'date' | 'select';
  selectableItems?: {id: string | number; value: string}[];
  defaultValue: string | undefined | null;
  label: string;
  StartAdornmentIcon: React.ReactNode;
  fieldName: string;
  mutateFn: (fieldName: string, value: string | number) => DocumentNode;
  isPatientDataLoading?: boolean;
  doctorId?: number;
  granularity?: string;
  dateFormat?: string | null;
  validateFn?: (value: string) => string | undefined;
  hideCalendarButton?: boolean;
  errorTitle?: string;
}

let currentlyEditing: string | null = null;

function EditableItem({
  defaultValue,
  label,
  StartAdornmentIcon,
  type = 'text',
  selectableItems,
  fieldName,
  mutateFn,
  isPatientDataLoading,
  doctorId,
  granularity = 'day',
  dateFormat,
  validateFn,
  errorTitle,
  hideCalendarButton,
}: EditableItemProps) {
  const {patientId} = useParams();
  const client = useApolloClient();
  const [isEditing, setIsEditing] = useState(false);
  const [validationError, setValidationError] = useState<string | undefined>(undefined);
  const [value, setValue] = useState(defaultValue ?? '');
  const [previewValue, setPreviewValue] = useState(defaultValue ?? '');
  const componentRef = useRef<HTMLDivElement>(null);
  const uniqueId = useRef(`${fieldName}-${Math.random().toString(36).substring(2)}`).current;

  const confirm = useConfirmation();

  useEffect(() => {
    setValue(defaultValue ?? '');
    setPreviewValue(defaultValue ?? '');
  }, [defaultValue]);

  useEffect(() => {
    const handleOutsideClick = (e: MouseEvent) => {
      const popoverElement = document.querySelector('.react-aria-Popover');
      if (popoverElement && popoverElement.contains(e.target as Node)) {
        return;
      }
      if (isEditing && componentRef.current && !componentRef.current.contains(e.target as Node)) {
        handleCancel();
      }
    };

    document.addEventListener('mousedown', handleOutsideClick);
    return () => {
      document.removeEventListener('mousedown', handleOutsideClick);
      if (currentlyEditing === uniqueId) {
        currentlyEditing = null;
      }
    };
  }, [isEditing]);

  const handleDoubleClick = () => {
    if (currentlyEditing && currentlyEditing !== uniqueId) {
      return;
    }
    currentlyEditing = uniqueId;
    setIsEditing(true);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (validationError) {
      setValidationError(undefined);
    }
    setValue(e.target.value);
  };

  const handleSubmit = async () => {
    if (validateFn) {
      const error = validateFn(value);
      if (error) {
        confirm({
          title: errorTitle ?? 'Invalid Input',
          description: error,
          actionLabel: 'Okay',
          callback: () => setValidationError(undefined),
        });
        return;
      }
    }

    if (value !== previewValue) {
      await client.mutate({
        mutation: mutateFn(fieldName, value),
        refetchQueries: [getPatientsDetailData, getDoctorsData],
        variables: doctorId ? {doctorId} : {patientId},
      });
      setPreviewValue(value);
    }
    setIsEditing(false);
    currentlyEditing = null;
  };

  const handleCancel = () => {
    setValue(previewValue);
    setIsEditing(false);
    currentlyEditing = null;
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (validateFn) {
        const error = validateFn((e.target as HTMLInputElement).value);
        if (!error) {
          handleSubmit();
          return;
        }
        confirm({
          title: errorTitle ?? 'Invalid Input',
          description: error,
          actionLabel: 'Okay',
        });
      }
    } else if (e.key === 'Escape') {
      handleCancel();
    }
  };

  const displayValue = useMemo(() => {
    if (type === 'select') {
      return selectableItems?.find((item) => item.id.toString() === previewValue)?.value;
    } else if (type === 'date' && previewValue && dateFormat) {
      try {
        return format(new Date(previewValue), dateFormat);
      } catch (error) {
        console.error('Error formatting date:', error);
        return previewValue;
      }
    } else {
      return previewValue;
    }
  }, [type, previewValue, selectableItems, dateFormat]);

  return (
    <div
      className="w-full items-center text-sm text-neutral-800"
      onDoubleClick={handleDoubleClick}
      ref={componentRef}
    >
      <div
        onKeyDown={handleKeyDown}
        className="group flex h-8 w-full items-center gap-x-2 rounded-md px-2 hover:bg-neutral-100"
      >
        <div className="flex shrink-0 items-center gap-x-2">
          {StartAdornmentIcon}
          {!isEditing && (
            <Label className="mb-0 text-xs font-normal text-neutral-700 capitalize">{label}:</Label>
          )}
        </div>

        <>
          {!isEditing && (
            <>
              {isPatientDataLoading ? (
                <Skeleton
                  height={10}
                  width={100}
                />
              ) : (
                <Tooltip>
                  <TooltipTrigger className="grow truncate text-left text-xs text-neutral-900">
                    {displayValue}
                  </TooltipTrigger>
                  <TooltipContent className={displayValue && displayValue.length < 20 ? 'hidden' : ''}>
                    {displayValue}
                  </TooltipContent>
                </Tooltip>
              )}
              {!isPatientDataLoading && (
                <button
                  className="hidden cursor-pointer group-hover:flex hover:flex"
                  onClick={() => {
                    if (!currentlyEditing || currentlyEditing === uniqueId) {
                      currentlyEditing = uniqueId;
                      setIsEditing(true);
                    }
                  }}
                >
                  <EditSquareIconly className="text-brand-500 h-4.5 w-4.5" />
                </button>
              )}
            </>
          )}

          {isEditing && (
            <FocusScope
              restoreFocus
              autoFocus
            >
              {(() => {
                switch (type) {
                  case 'text':
                  case 'number':
                    return (
                      <Input
                        type={type}
                        className="focus-visible:outline-brand-500 grow rounded-sm p-1 px-2 py-1 text-xs focus:border-0 focus:ring-0 focus-visible:outline-1"
                        value={value}
                        onChange={handleChange}
                        onKeyDown={handleKeyDown}
                        aria-label={label}
                        onBlur={(e) => {
                          const error = validateFn ? validateFn(e.target.value) : undefined;
                          if (error) {
                            setValidationError(error);
                          }
                        }}
                        autoFocus
                      />
                    );
                  case 'select':
                    return (
                      <Select
                        className="react-aria-Select flex grow items-center justify-between"
                        defaultSelectedKey={value}
                        onSelectionChange={(key) => {
                          setValue(key?.toString() ?? '');
                          // handleSubmit();
                        }}
                        aria-label={label}
                        onKeyDown={handleKeyDown}
                      >
                        <RACButton
                          autoFocus={true}
                          className="react-aria-Button w-full max-w-40 p-0.5 px-3"
                        >
                          <SelectValue className="react-aria-SelectValue w-full text-sm" />
                          <ChevronDown />
                        </RACButton>
                        <Popover className="react-aria-Popover min-w-65 truncate overflow-auto">
                          <ListBox items={selectableItems}>
                            {(item) => (
                              <ListBoxItem
                                id={item.id}
                                textValue={item.value}
                              >
                                <div className="flex w-full cursor-pointer items-center justify-between text-xs">
                                  <span className="w-40 truncate">{item.value}</span>
                                  <Check className="text-white-white hidden h-4 w-4 [[data-selected=true]_&]:block" />
                                </div>
                              </ListBoxItem>
                            )}
                          </ListBox>
                        </Popover>
                      </Select>
                    );
                  case 'date':
                    return (
                      <DatePicker
                        autoFocus={true}
                        granularity={granularity as any}
                        hourCycle={24}
                        shouldForceLeadingZeros
                        className="react-aria-DatePicker grow py-0"
                        defaultValue={toCalendarDateTime(
                          fromDate(value ? new Date(value) : new Date(), getLocalTimeZone())
                        )}
                        onKeyDown={handleKeyDown}
                        onChange={(dateTime) => {
                          if (dateTime) {
                            const [date] = dateTime.toString().split('T');
                            setValue(date);
                            // handleSubmit();
                          }
                        }}
                        aria-label={label}
                      >
                        <DateTimeSelector className="react-aria-Group w-full rounded-sm bg-white py-0 text-xs">
                          <RACButton
                            className={clsx('react-aria-Button', hideCalendarButton ? 'hidden' : '')}
                          >
                            <CalenderIcon
                              className="h-4 w-4"
                              color="currentColor"
                            />
                          </RACButton>
                        </DateTimeSelector>
                        <Popover className="react-aria-Popover w-max">
                          <Dialog>
                            <Calendar />
                          </Dialog>
                        </Popover>
                      </DatePicker>
                    );
                  default:
                    return (
                      <Input
                        type={type}
                        className="grow p-1 text-xs"
                        value={value}
                        onChange={handleChange}
                        onKeyDown={handleKeyDown}
                        aria-label={label}
                      />
                    );
                }
              })()}
              <div className="ml-2 flex items-center gap-x-2">
                <button
                  className="text-brand-500 focus-visible:outline-brand-500 cursor-pointer rounded-sm border p-1 focus-visible:outline-1"
                  aria-label="Cancel"
                  onClick={handleCancel}
                >
                  <CloseRemoveIconly className="h-4 w-4" />
                </button>
                <button
                  className="text-brand-500 focus-visible:outline-brand-500 cursor-pointer rounded-sm border p-1 focus-visible:outline-1"
                  aria-label="Submit"
                  onClick={handleSubmit}
                >
                  <CheckmarkIconly className="h-4 w-4" />
                </button>
              </div>
            </FocusScope>
          )}
        </>
      </div>
    </div>
  );
}

export default EditableItem;
