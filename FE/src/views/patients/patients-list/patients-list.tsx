import {useState} from 'react';
import {Cell, Column, Input, Row, Table, TableHeader} from 'react-aria-components';

import {useQuery} from '@apollo/client';
import {useDebouncedValue} from '@mantine/hooks';
import {format, parseISO} from 'date-fns';
import {ChevronRight as ChevronRightIcon} from 'lucide-react';
import {parseAsString, useQueryState} from 'nuqs';

import {LoadableTableBody} from '@/components/Table.tsx';
import {Button} from '@/components/ui/button';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import {searchPatientsQuery} from '@/graphql/patients.ts';
import FilterButton from '@/views/patients/components/filter-button';
import {SearchIconly} from '@/components/icons/SearchIconIconly.tsx';
import {useDialogState} from "@/components/modal-state-provider.tsx";
import AddPatient from "@/views/patients/components/add-patient.tsx";
import authStore from "@/store/auth.store.ts";

const genderOptions = [
  {id: 'M', textValue: 'Male'},
  {id: 'F', textValue: 'Female'},
  {id: 'U', textValue: 'Non-binary'},
];

export default function PatientList() {
  const [searchValue] = useQueryState('search', parseAsString.withDefault(''));
  const [search, setSearch] = useState(searchValue ? searchValue.trim() : '');
  const [debouncedSearch] = useDebouncedValue(search, 300);
  const [pagination, setPagination] = useState({pageIndex: 0, pageSize: 20});
  const [selectedGenders, setSelectedGenders] = useState<typeof genderOptions>([]);
  const [, setAddPatient] = useDialogState('add-patient');

  const {data, previousData, loading} = useQuery(searchPatientsQuery, {
    variables: {
      searchText: debouncedSearch,
      genderCodes: (selectedGenders.length > 0
        ? `{${selectedGenders.map((g) => g.id).join()}}`
        : null) as any,
      siteId: authStore.tokenSiteId?.toString(),
      limit: pagination.pageSize,
      offset: pagination.pageIndex * pagination.pageSize,
    },
  });

  const totalPages = Math.ceil((data?.search_pas_pt_aggregate?.aggregate?.count ?? 0) / pagination.pageSize);
  return (
    <div className="flex h-full flex-col gap-4">
      <div className="flex w-full gap-2">
        <div className="relative w-90">
          <Input
            placeholder="Search patients"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="peer ring-brand2-400/30 focus-visible:!border-brand2-500 h-8 w-full rounded-sm !border !border-gray-300 bg-white px-4 pl-8 text-sm text-gray-600 outline-none placeholder:text-neutral-700 focus-visible:ring-3"
          />

          <div className="peer-focus:text-brand2-500 pointer-events-none absolute top-1.75 left-2 text-neutral-400">
            <SearchIconly className="h-4.5 w-4.5" />
          </div>
        </div>

        <FilterButton
          selectedItems={selectedGenders}
          onSelectionChange={setSelectedGenders}
          label={'Gender'}
          items={genderOptions}
          disabled={loading}
        />

        <Button onPress={() => setAddPatient(true)} className="react-aria-Button ml-auto h-8">Add Patient</Button>
        <AddPatient />
      </div>
      <Table
        aria-label="Patient List"
        className="react-aria-Table has-[.table-empty]:grow"
      >
        <TableHeader>
          <Column isRowHeader>MRN</Column>
          <Column>NAME</Column>
          <Column>DOB</Column>
          <Column>Gender</Column>
          <Column>MOBILE#</Column>
          <Column className="react-aria-Column">TOTAL TESTS</Column>
          <Column className="react-aria-Column"></Column>
        </TableHeader>

        <LoadableTableBody
          emptyTitle="No patients found"
          emptyDescription="Try refining your search or add a new patient"
          items={(loading ? previousData?.search_pas_pt : data?.search_pas_pt) ?? []}
          isLoading={loading}
          columnCount={7}
        >
          {(patient) => {
            const fullName = [patient.surname, patient.firstname].filter(Boolean).join(', ').toLowerCase();
            return (
              <Row
                id={patient.patientid ?? undefined}
                href={`/patients/${patient.patientid}`}
              >
                <Cell className="react-aria-Cell min-w-20 text-sm text-neutral-700">{patient.ur}</Cell>
                <Cell className="react-aria-Cell text-neutral-900 capitalize">
                  {patient.title ? `${patient.title.toLowerCase()}. ${fullName}` : fullName}
                </Cell>
                <Cell>
                  {patient.dob ? format(parseISO(patient.dob as unknown as string), 'dd MMMM, yyyy') : null}
                </Cell>
                <Cell>
                  {patient.gender_code === 'M' && 'Male'}
                  {patient.gender_code === 'F' && 'Female'}
                  {patient.gender_code === 'U' && 'Unknown'}
                  {['', null, undefined].includes(patient.gender_code) && ''}
                </Cell>
                <Cell>{patient.phone_mobile}</Cell>
                {patient.patientid && <Cell className="react-aria-Cell text-end"></Cell>}
                <Cell className="react-aria-Cell w-20">
                  <div className="flex items-center justify-end pr-4">
                    <ChevronRightIcon className="h-4 w-4 cursor-pointer text-neutral-500 transition-colors hover:text-neutral-700" />
                  </div>
                </Cell>
              </Row>
            );
          }}
        </LoadableTableBody>
      </Table>

      {totalPages > 1 && (
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                disabled={pagination.pageIndex === 0}
                onClick={() =>
                  setPagination((prev) => ({
                    ...prev,
                    pageIndex: Math.max(prev.pageIndex - 1, 0),
                  }))
                }
              />
            </PaginationItem>

            {[...Array(totalPages)].map((_, index) => (
              <PaginationItem key={index}>
                <PaginationLink
                  onClick={() => setPagination((prev) => ({...prev, pageIndex: index}))}
                  isActive={pagination.pageIndex === index}
                >
                  {index + 1}
                </PaginationLink>
              </PaginationItem>
            ))}

            <PaginationItem>
              <PaginationNext
                disabled={pagination.pageIndex + 1 >= totalPages}
                onClick={() => setPagination((prev) => ({...prev, pageIndex: prev.pageIndex + 1}))}
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      )}
    </div>
  );
}
