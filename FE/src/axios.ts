import axios, {AxiosError} from 'axios';

import {Paths} from '@/api-types/routePaths';

let authStore: any;

const getAuthStore = async () => {
  if (!authStore) {
    const module = await import('@/store/auth.store.ts');
    authStore = module.default;
  }
  return authStore;
};

const getCurrentToken = async (): Promise<string | null> => {
  try {
    const store = await getAuthStore();
    if (store.accessToken) {
      return store.accessToken;
    }
  } catch (error) {
    console.log(error)
  }

  return localStorage.getItem('rezibase:access_token');
};

export const axiosInstance = axios.create({
  withCredentials: true,
});

axiosInstance.interceptors.request.use(
  async (config) => {
    const token = await getCurrentToken();

    if (token && token !== 'undefined') {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

export function setLocalStorage(key: string, value: string) {
  localStorage.setItem(key, value);
  window.dispatchEvent(new Event('local-storage'));
  window.dispatchEvent(
    new CustomEvent('mantine-local-storage', {
      detail: {key, value},
    })
  );
}

export function removeLocalStorage(key: string) {
  localStorage.removeItem(key);
  window.dispatchEvent(new Event('local-storage'));
  window.dispatchEvent(
    new CustomEvent('mantine-local-storage', {
      detail: {key},
    })
  );
}

let refreshTokenRequest: Promise<any> | undefined;

axiosInstance.interceptors.response.use(
  (res) => res,
  async (err) => {
    const store = await getAuthStore();
    const refreshToken = store.refreshToken || localStorage.getItem('rezibase:refresh_token');

    if ([422, 401].includes(err.response?.status) && refreshToken && refreshToken !== 'undefined') {
      let tokenRes;

      if (!refreshTokenRequest) {
        const siteId = localStorage.getItem('rezibase:site_id');

        refreshTokenRequest = axios.post(
          Paths.REFRESH_TOKEN,
          {
            ...(siteId && {site_id: siteId}),
          },
          {
            headers: {
              Authorization: `Bearer ${refreshToken}`,
            },
          }
        );
      }

      try {
        tokenRes = await refreshTokenRequest;

        const newAccessToken = tokenRes.data.access_token;
        const newRefreshToken = tokenRes.data.refresh_token || refreshToken;

        setLocalStorage('rezibase:access_token', newAccessToken);
        if (tokenRes.data.refresh_token) {
          setLocalStorage('rezibase:refresh_token', newRefreshToken);
        }

        store.setAccessToken(newAccessToken);
        if (tokenRes.data.refresh_token) {
          store.refreshToken = newRefreshToken;
        }

        err.config.headers.Authorization = `Bearer ${newAccessToken}`;
        return axios.request(err.config);
      } catch (refreshTokenError) {
        if ([422, 401].includes((refreshTokenError as AxiosError).response?.status as number)) {
          removeLocalStorage('rezibase:access_token');
          removeLocalStorage('rezibase:refresh_token');

          store.setAccessToken(null);
          store.refreshToken = null;

          if (!store.isSwitchingSite) {
            store.logout();
          }
        }
      } finally {
        refreshTokenRequest = undefined;
      }
    }

    if (err.response?.status === 401 && !refreshToken) {
      const store = await getAuthStore();
      if (!store.isSwitchingSite) {
        store.logout();
      }
    }

    throw err;
  }
);

export function setHeaders(accessToken?: string) {
  if (accessToken) {
    axiosInstance.defaults.headers.common.Authorization = `Bearer ${accessToken}`;
  } else {
    delete axiosInstance.defaults.headers.common.Authorization;
  }
}