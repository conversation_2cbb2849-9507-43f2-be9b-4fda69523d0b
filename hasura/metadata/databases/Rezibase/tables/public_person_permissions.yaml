table:
  name: person_permissions
  schema: public
object_relationships:
  - name: permission
    using:
      manual_configuration:
        column_mapping:
          permissionid: permtypeid
        insertion_order: null
        remote_table:
          name: list_permissiontypes
          schema: public
  - name: person
    using:
      foreign_key_constraint_on: personid
select_permissions:
  - role: user
    permission:
      columns:
        - value
        - permissionid
        - personid
        - person_permissionid
        - lastupdated
      filter: {}
    comment: ""
event_triggers:
  - name: person_permissions
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
