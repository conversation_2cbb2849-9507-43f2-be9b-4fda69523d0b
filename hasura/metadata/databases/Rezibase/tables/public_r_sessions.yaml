table:
  name: r_sessions
  schema: public
object_relationships:
  - name: pas_pt
    using:
      foreign_key_constraint_on: patientid
insert_permissions:
  - role: user
    permission:
      check:
        pas_pt:
          site_id:
            _eq: X-Hasura-Site-Id
      columns:
        - admissionstatus
        - billing_billedto
        - billing_billingmo
        - billing_billingmoproviderno
        - diagnosticcategory
        - height
        - lab
        - lastupdated_session
        - lastupdatedby_session
        - patientid
        - pred_sourceids
        - report_copyto
        - report_copyto_2
        - req_address
        - req_clinicalnotes
        - req_date
        - req_email
        - req_fax
        - req_healthservice_code
        - req_healthservice_text
        - req_name
        - req_phone
        - req_providernumber
        - req_time
        - smoke_cigsperday
        - smoke_hx
        - smoke_last
        - smoke_packyears
        - smoke_yearssmoked
        - testdate
        - weight
    comment: ""
select_permissions:
  - role: user
    permission:
      columns:
        - admissionstatus
        - billing_billedto
        - billing_billingmo
        - billing_billingmoproviderno
        - diagnosticcategory
        - height
        - lab
        - lastupdatedby_session
        - report_copyto
        - report_copyto_2
        - req_address
        - req_email
        - req_fax
        - req_healthservice_code
        - req_healthservice_text
        - req_name
        - req_phone
        - req_providernumber
        - smoke_cigsperday
        - smoke_hx
        - smoke_last
        - smoke_packyears
        - smoke_yearssmoked
        - weight
        - req_date
        - testdate
        - patientid
        - sessionid
        - pred_sourceids
        - req_clinicalnotes
        - lastupdated_session
        - req_time
      filter:
        pas_pt:
          site_id:
            _eq: X-Hasura-Site-Id
    comment: ""
update_permissions:
  - role: user
    permission:
      columns:
        - admissionstatus
        - billing_billedto
        - billing_billingmo
        - billing_billingmoproviderno
        - diagnosticcategory
        - height
        - lab
        - lastupdated_session
        - lastupdatedby_session
        - patientid
        - pred_sourceids
        - report_copyto
        - report_copyto_2
        - req_address
        - req_clinicalnotes
        - req_date
        - req_email
        - req_fax
        - req_healthservice_code
        - req_healthservice_text
        - req_name
        - req_phone
        - req_providernumber
        - req_time
        - smoke_cigsperday
        - smoke_hx
        - smoke_last
        - smoke_packyears
        - smoke_yearssmoked
        - testdate
        - weight
      filter:
        pas_pt:
          site_id:
            _eq: X-Hasura-Site-Id
      check:
        pas_pt:
          site_id:
            _eq: X-Hasura-Site-Id
    comment: ""
event_triggers:
  - name: r_sessions
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
