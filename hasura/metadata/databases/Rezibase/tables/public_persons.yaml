table:
  name: persons
  schema: public
array_relationships:
  - name: person_permissions
    using:
      foreign_key_constraint_on:
        column: personid
        table:
          name: person_permissions
          schema: public
select_permissions:
  - role: user
    permission:
      columns:
        - enabled
        - postcode
        - state
        - address_1
        - address_2
        - department
        - email
        - firstname
        - gender
        - institution
        - login_healthservice_code_default
        - phone_home
        - phone_mobile
        - phone_work
        - profession_category
        - suburb
        - surname
        - title
        - user_name
        - user_password
        - dob
        - personid
        - last_login
        - lastupdated
      filter: {}
    comment: ""
event_triggers:
  - name: persons
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
