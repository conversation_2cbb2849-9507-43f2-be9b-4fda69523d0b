table:
  name: equipment_companies
  schema: public
array_relationships:
  - name: equipments
    using:
      foreign_key_constraint_on:
        column: equipment_distributor_id
        table:
          name: equipments
          schema: public
  - name: equipmentsByEquipmentManufacturerId
    using:
      foreign_key_constraint_on:
        column: equipment_manufacturer_id
        table:
          name: equipments
          schema: public
event_triggers:
  - name: equipment_companies
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
