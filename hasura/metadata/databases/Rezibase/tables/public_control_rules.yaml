table:
  name: control_rules
  schema: public
array_relationships:
  - name: alarm_control_rules
    using:
      foreign_key_constraint_on:
        column: control_rule_id
        table:
          name: alarm_control_rules
          schema: public
  - name: control_methods_equipments_parameters_control_rules
    using:
      foreign_key_constraint_on:
        column: control_rule_id
        table:
          name: control_methods_equipments_parameters_control_rules
          schema: public
select_permissions:
  - role: user
    permission:
      columns:
        - name
        - id
        - description
        - created
        - updated
      filter: {}
    comment: ""
event_triggers:
  - name: control_rules
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
