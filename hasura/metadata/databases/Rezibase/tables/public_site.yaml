table:
  name: site
  schema: public
array_relationships:
  - name: labs
    using:
      foreign_key_constraint_on:
        column: site__ref_id
        table:
          name: labs
          schema: public
  - name: site_settings
    using:
      foreign_key_constraint_on:
        column: site_id
        table:
          name: site_settings
          schema: public
  - name: user_site_controls
    using:
      foreign_key_constraint_on:
        column: site_id
        table:
          name: user_site_control
          schema: public
  - name: user_sites
    using:
      foreign_key_constraint_on:
        column: site_id
        table:
          name: user_sites
          schema: public
select_permissions:
  - role: user
    permission:
      columns:
        - enable_otp
        - name
        - id
        - created
        - updated
      filter: {}
    comment: ""
event_triggers:
  - name: site
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
