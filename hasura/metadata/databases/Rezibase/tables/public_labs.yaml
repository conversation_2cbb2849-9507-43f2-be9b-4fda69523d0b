table:
  name: labs
  schema: public
object_relationships:
  - name: site
    using:
      foreign_key_constraint_on: site__ref_id
array_relationships:
  - name: ab_users
    using:
      foreign_key_constraint_on:
        column: selected_lab_id
        table:
          name: ab_user
          schema: public
  - name: equipments
    using:
      foreign_key_constraint_on:
        column: lab_id
        table:
          name: equipments
          schema: public
computed_fields:
  - name: lab_parameters
    definition:
      function:
        name: get_lab_parameters
        schema: public
select_permissions:
  - role: user
    permission:
      columns:
        - id
        - name
        - site__ref_id
        - created
        - updated
      filter: {}
    comment: ""
event_triggers:
  - name: labs
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
