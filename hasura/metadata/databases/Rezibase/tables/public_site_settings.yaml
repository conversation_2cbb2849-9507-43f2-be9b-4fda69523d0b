table:
  name: site_settings
  schema: public
object_relationships:
  - name: site
    using:
      foreign_key_constraint_on: site_id
select_permissions:
  - role: user
    permission:
      columns:
        - description
        - name
        - value
        - id
        - site_id
        - value_json
        - created
        - updated
      filter:
        site_id:
          _eq: X-Hasura-Site-Id
    comment: ""
event_triggers:
  - name: site_settings
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
