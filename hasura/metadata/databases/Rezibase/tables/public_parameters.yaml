table:
  name: parameters
  schema: public
array_relationships:
  - name: control_methods_equipments_parameters
    using:
      foreign_key_constraint_on:
        column: parameter_id
        table:
          name: control_methods_equipments_parameters
          schema: public
select_permissions:
  - role: user
    permission:
      columns:
        - active
        - abbreviation
        - inactivity_reason
        - long_name
        - unit
        - decimal_places
        - id
        - created
        - updated
        - inactivity_date
      filter: {}
    comment: ""
event_triggers:
  - name: parameters
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
