table:
  name: pas_pt_ur_numbers
  schema: public
object_relationships:
  - name: pas_pt
    using:
      foreign_key_constraint_on: patientid
computed_fields:
  - name: health_service
    definition:
      function:
        name: get_ur_health_service
        schema: public
insert_permissions:
  - role: user
    permission:
      check: {}
      columns:
        - create_by
        - create_date
        - created_inreslab_xx
        - patientid
        - ur
        - ur_hsid
        - ur_status
    comment: ""
select_permissions:
  - role: user
    permission:
      columns:
        - created_inreslab_xx
        - create_by
        - ur
        - ur_hsid
        - ur_status
        - patientid
        - ur_id
        - create_date
      filter:
        pas_pt:
          site_id:
            _eq: X-Hasura-Site-Id
    comment: ""
update_permissions:
  - role: user
    permission:
      columns:
        - create_by
        - create_date
        - created_inreslab_xx
        - patientid
        - ur
        - ur_hsid
        - ur_status
      filter: {}
      check: null
    comment: ""
event_triggers:
  - name: pas_pt_ur_numbers
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
