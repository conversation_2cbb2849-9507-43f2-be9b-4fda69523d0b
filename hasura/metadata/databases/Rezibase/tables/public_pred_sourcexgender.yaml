table:
  name: pred_sourcexgender
  schema: public
object_relationships:
  - name: pred_ref_gender
    using:
      foreign_key_constraint_on: genderid
insert_permissions:
  - role: user
    permission:
      check: {}
      columns:
        - genderid
        - sourceid
    comment: ""
select_permissions:
  - role: user
    permission:
      columns:
        - genderid
        - id
        - sourceid
      filter: {}
    comment: ""
update_permissions:
  - role: user
    permission:
      columns:
        - genderid
        - sourceid
      filter: {}
      check: null
    comment: ""
event_triggers:
  - name: pred_sourcexgender
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
