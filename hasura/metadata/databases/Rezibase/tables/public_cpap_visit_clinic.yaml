table:
  name: cpap_visit_clinic
  schema: public
object_relationships:
  - name: pas_pt
    using:
      foreign_key_constraint_on: patientid
array_relationships:
  - name: cpap_visit_treatment_settings
    using:
      foreign_key_constraint_on:
        column: cpap_visit_clinic_id
        table:
          name: cpap_visit_treatment_setting
          schema: public
select_permissions:
  - role: user
    permission:
      columns:
        - admission_status
        - clinical_adherence_hour_meter
        - clinical_adherence_objective_hrs
        - clinical_adherence_objective_nights
        - clinical_adherence_period
        - clinical_adherence_reported_days
        - clinical_adherence_reported_hrs
        - clinical_adherence_reported_nights
        - clinical_adherence_time
        - clinical_cpap_issues_condensation
        - clinical_cpap_issues_dry_mouth
        - clinical_cpap_issues_dry_nose
        - clinical_cpap_issues_pressure
        - clinical_cpap_issues_runny_nose
        - clinical_mask_air_leaks
        - clinical_mask_comfortable
        - clinical_mask_face_soreness
        - clinical_mask_sore_eyes
        - clinical_symptoms_daytime_napping
        - clinical_symptoms_eds
        - clinical_symptoms_ess
        - clinical_symptoms_headache
        - clinician
        - device_treatment_mode
        - last_updated_by
        - location
        - referred_by
        - report_amendedby
        - report_authorisedby
        - report_reportedby
        - report_status
        - report_to
        - report_verifiedby
        - vinah_contact_delivery_mode
        - vinah_contact_professional_group
        - vinah_contact_purpose
        - vinah_delivery_setting
        - visit_type
        - report_amendeddate
        - report_authoriseddate
        - report_reporteddate
        - report_verifieddate
        - cpap_visit_clinic_id
        - device_treatment_mode_id
        - patientid
        - therapyequip_id
        - address
        - clinical_notes
        - referral_notes
        - clinical_date_treatment_commenced
        - last_updated
        - vinah_contact_end_datetime
        - vinah_contact_start_datetime
        - visit_datetime
      filter: {}
      allow_aggregations: true
    comment: ""
event_triggers:
  - name: cpap_visit_clinic
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
