table:
  name: rft_test_quality
  schema: public
insert_permissions:
  - role: user
    permission:
      check: {}
      columns:
        - comment
        - grade
        - param_id
        - test_id
    comment: ""
select_permissions:
  - role: user
    permission:
      columns:
        - grade
        - id
        - param_id
        - test_id
        - comment
      filter: {}
    comment: ""
update_permissions:
  - role: user
    permission:
      columns:
        - comment
        - grade
        - param_id
        - test_id
      filter: {}
      check: {}
    comment: ""
event_triggers:
  - name: rft_test_quality
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
