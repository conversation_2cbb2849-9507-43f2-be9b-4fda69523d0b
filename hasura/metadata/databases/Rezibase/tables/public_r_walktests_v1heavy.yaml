table:
  name: r_walktests_v1heavy
  schema: public
object_relationships:
  - name: pas_pt
    using:
      foreign_key_constraint_on: patientid
  - name: r_session
    using:
      foreign_key_constraint_on: sessionid
select_permissions:
  - role: user
    permission:
      columns:
        - bdstatus
        - lab
        - lastupdatedby_walk
        - o2device_type
        - o2device_units
        - report_amendedby
        - report_authorisedby
        - report_reportedby
        - report_status
        - report_verifiedby
        - scientist
        - testtype
        - walktype
        - report_amendeddate
        - report_authoriseddate
        - report_reporteddate
        - report_verifieddate
        - patientid
        - protocolid
        - sessionid
        - walkid
        - report_amendednotes
        - report_text
        - technicalnotes
        - lastupdated_walk
        - testtime
      filter: {}
      allow_aggregations: true
    comment: ""
event_triggers:
  - name: r_walktests_v1heavy
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
