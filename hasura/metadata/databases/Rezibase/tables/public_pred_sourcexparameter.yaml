table:
  name: pred_sourcexparameter
  schema: public
object_relationships:
  - name: parameter
    using:
      manual_configuration:
        column_mapping:
          paramid: parameterid
        insertion_order: null
        remote_table:
          name: pred_ref_parameters
          schema: public
insert_permissions:
  - role: user
    permission:
      check: {}
      columns:
        - paramid
        - sourceid
    comment: ""
select_permissions:
  - role: user
    permission:
      columns:
        - paramid
        - sourceid
        - sxpid
      filter: {}
    comment: ""
update_permissions:
  - role: user
    permission:
      columns:
        - paramid
        - sourceid
      filter: {}
      check: null
    comment: ""
event_triggers:
  - name: pred_sourcexparameter
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
