table:
  name: equipments
  schema: public
object_relationships:
  - name: equipmentCompanyByEquipmentManufacturerId
    using:
      foreign_key_constraint_on: equipment_manufacturer_id
  - name: equipment_company
    using:
      foreign_key_constraint_on: equipment_distributor_id
  - name: lab
    using:
      foreign_key_constraint_on: lab_id
array_relationships:
  - name: control_methods
    using:
      foreign_key_constraint_on:
        column: controlling_equipment_id
        table:
          name: control_methods
          schema: public
  - name: control_methods_equipments
    using:
      foreign_key_constraint_on:
        column: equipment_id
        table:
          name: control_methods_equipments
          schema: public
  - name: equipment_documents
    using:
      foreign_key_constraint_on:
        column: equipment_id
        table:
          name: equipment_documents
          schema: public
  - name: equipment_logs
    using:
      foreign_key_constraint_on:
        column: equipment_id
        table:
          name: equipment_logs
          schema: public
event_triggers:
  - name: equipments
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
