table:
  name: user_sites
  schema: public
object_relationships:
  - name: ab_user
    using:
      foreign_key_constraint_on: user_id
  - name: site
    using:
      foreign_key_constraint_on: site_id
select_permissions:
  - role: user
    permission:
      columns:
        - created
        - id
        - site_id
        - updated
        - user_enable_otp
        - user_id
      filter: {}
    comment: ""
event_triggers:
  - name: user_sites
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
