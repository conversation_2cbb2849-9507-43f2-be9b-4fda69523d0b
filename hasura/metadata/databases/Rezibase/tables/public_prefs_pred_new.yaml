table:
  name: prefs_pred_new
  schema: public
object_relationships:
  - name: equation
    using:
      manual_configuration:
        column_mapping:
          equationid: id
        insertion_order: null
        remote_table:
          name: pred_equations
          schema: public
  - name: pred_ref_clipmethod_age
    using:
      foreign_key_constraint_on: age_clipmethodid
  - name: pred_ref_clipmethod_ht
    using:
      foreign_key_constraint_on: ht_clipmethodid
  - name: pred_ref_clipmethod_wt
    using:
      foreign_key_constraint_on: wt_clipmethodid
insert_permissions:
  - role: user
    permission:
      check:
        site_id:
          _eq: X-Hasura-Site-Id
      set:
        site_id: x-hasura-Site-Id
      columns:
        - active
        - age_clipmethod
        - age_clipmethodid
        - enddate
        - equationid
        - ht_clipmethod
        - ht_clipmethodid
        - lastedit
        - lasteditby
        - markfordeletion
        - startdate
        - wt_clipmethod
        - wt_clipmethodid
    comment: ""
select_permissions:
  - role: user
    permission:
      columns:
        - active
        - age_clipmethod
        - ht_clipmethod
        - lasteditby
        - wt_clipmethod
        - enddate
        - startdate
        - age_clipmethodid
        - equationid
        - ht_clipmethodid
        - markfordeletion
        - prefid
        - wt_clipmethodid
        - lastedit
      filter:
        site_id:
          _eq: X-Hasura-Site-Id
    comment: ""
update_permissions:
  - role: user
    permission:
      columns:
        - active
        - age_clipmethod
        - age_clipmethodid
        - enddate
        - equationid
        - ht_clipmethod
        - ht_clipmethodid
        - lastedit
        - lasteditby
        - markfordeletion
        - startdate
        - wt_clipmethod
        - wt_clipmethodid
      filter:
        site_id:
          _eq: X-Hasura-Site-Id
      check:
        site_id:
          _eq: X-Hasura-Site-Id
    comment: ""
delete_permissions:
  - role: user
    permission:
      filter:
        site_id:
          _eq: X-Hasura-Site-Id
    comment: ""
event_triggers:
  - name: prefs_pred_new
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
