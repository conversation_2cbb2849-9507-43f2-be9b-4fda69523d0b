table:
  name: pred_sourcexethnicity
  schema: public
object_relationships:
  - name: ethnicity
    using:
      manual_configuration:
        column_mapping:
          ethnicityid: id
        insertion_order: null
        remote_table:
          name: pred_ref_ethnicities
          schema: public
insert_permissions:
  - role: user
    permission:
      check: {}
      columns:
        - ethnicityid
        - sourceid
    comment: ""
select_permissions:
  - role: user
    permission:
      columns:
        - ethnicityid
        - id
        - sourceid
      filter: {}
    comment: ""
event_triggers:
  - name: pred_sourcexethnicity
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
