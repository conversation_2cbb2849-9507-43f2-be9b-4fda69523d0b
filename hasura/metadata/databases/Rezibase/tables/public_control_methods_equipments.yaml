table:
  name: control_methods_equipments
  schema: public
object_relationships:
  - name: control_method
    using:
      foreign_key_constraint_on: control_method_id
  - name: equipment
    using:
      foreign_key_constraint_on: equipment_id
array_relationships:
  - name: control_methods_equipments_parameters
    using:
      foreign_key_constraint_on:
        column: control_method_equipment_id
        table:
          name: control_methods_equipments_parameters
          schema: public
  - name: sessions
    using:
      foreign_key_constraint_on:
        column: control_method_equipment_id
        table:
          name: sessions
          schema: public
event_triggers:
  - name: control_methods_equipments
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
