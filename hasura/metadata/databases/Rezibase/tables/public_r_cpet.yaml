table:
  name: r_cpet
  schema: public
object_relationships:
  - name: pas_pt
    using:
      foreign_key_constraint_on: patientid
  - name: r_session
    using:
      foreign_key_constraint_on: sessionid
select_permissions:
  - role: user
    permission:
      columns:
        - bdstatus
        - lab
        - lastupdatedby_cpet
        - r_at_vo2
        - r_at_vo2_pc
        - r_at_vo2_pc_mpv
        - r_at_vo2_pc_ref
        - r_endex_bp
        - report_amendedby
        - report_authorisedby
        - report_reportedby
        - report_status
        - report_verifiedby
        - r_max_hr
        - r_max_hr_mpv
        - r_max_hr_ref
        - r_max_hr_reserve
        - r_max_hr_reserve_mpv
        - r_max_hr_reserve_ref
        - r_max_o2pulse
        - r_max_o2pulse_mpv
        - r_max_o2pulse_ref
        - r_max_vco2
        - r_max_vco2_mpv
        - r_max_vco2_ref
        - r_max_ve
        - r_max_ve_mpv
        - r_max_ve_ref
        - r_max_ve_reserve
        - r_max_ve_reserve_mpv
        - r_max_ve_reserve_ref
        - r_max_vo2
        - r_max_vo2kg
        - r_max_vo2kg_mpv
        - r_max_vo2kg_ref
        - r_max_vo2_mpv
        - r_max_vo2_ref
        - r_max_vt
        - r_max_vt_mpv
        - r_max_vt_ref
        - r_max_workload
        - r_max_workload_mpv
        - r_max_workload_ref
        - r_nadir_spo2
        - r_nadir_vevco2
        - r_nadir_vevco2_mpv
        - r_nadir_vevco2_ref
        - r_rest_bp
        - r_rest_spo2
        - r_slope_workload_vo2
        - r_slope_workload_vo2_mpv
        - r_slope_workload_vo2_ref
        - r_spiro_pre_fev1
        - r_spiro_pre_fvc
        - r_spiro_pre_ic
        - r_symptoms_dyspnoea_borg
        - r_symptoms_legs_borg
        - r_symptoms_other_borg
        - scientist
        - testtype
        - report_amendeddate
        - report_authoriseddate
        - report_reporteddate
        - report_verifieddate
        - cpetid
        - import_file_id
        - patientid
        - sessionid
        - report_amendednotes
        - report_text
        - technicalnotes
        - lastupdated_cpet
        - testtime
      filter: {}
      allow_aggregations: true
    comment: ""
event_triggers:
  - name: r_cpet
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
