table:
  name: alarms
  schema: public
object_relationships:
  - name: ab_user
    using:
      foreign_key_constraint_on: closed_by_id
  - name: session_datum
    using:
      foreign_key_constraint_on: session_data_id
array_relationships:
  - name: alarm_control_rules
    using:
      foreign_key_constraint_on:
        column: alarm_id
        table:
          name: alarm_control_rules
          schema: public
select_permissions:
  - role: user
    permission:
      columns:
        - open
        - closed_by_id
        - id
        - session_data_id
        - notes
        - created
        - updated
      filter: {}
      allow_aggregations: true
    comment: ""
event_triggers:
  - name: alarms
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
