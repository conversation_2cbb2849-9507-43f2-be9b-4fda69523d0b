table:
  name: session_data
  schema: public
object_relationships:
  - name: ab_user
    using:
      foreign_key_constraint_on: closed_by_id
  - name: control_methods_equipments_parameter
    using:
      foreign_key_constraint_on: control_method_equipment_parameter_id
  - name: session
    using:
      foreign_key_constraint_on: session_id
array_relationships:
  - name: alarms
    using:
      foreign_key_constraint_on:
        column: session_data_id
        table:
          name: alarms
          schema: public
select_permissions:
  - role: user
    permission:
      columns:
        - open
        - parameter_value
        - sr_value
        - closed_by_id
        - control_method_equipment_parameter_id
        - id
        - session_id
        - notes
        - created
        - updated
      filter: {}
    comment: ""
event_triggers:
  - name: session_data
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
