table:
  name: pas_pt
  schema: public
object_relationships:
  - name: gender_rft
    using:
      manual_configuration:
        column_mapping:
          gender_forrfts_code: genderid
        insertion_order: null
        remote_table:
          name: pred_ref_genders
          schema: public
  - name: pas_pt_site
    using:
      manual_configuration:
        column_mapping:
          site_id: id
        insertion_order: null
        remote_table:
          name: site
          schema: public
array_relationships:
  - name: cpap_visit_clinics
    using:
      foreign_key_constraint_on:
        column: patientid
        table:
          name: cpap_visit_clinic
          schema: public
  - name: cpap_visit_treatment_settings
    using:
      foreign_key_constraint_on:
        column: patientid
        table:
          name: cpap_visit_treatment_setting
          schema: public
  - name: pas_pt_addresses
    using:
      foreign_key_constraint_on:
        column: patientid
        table:
          name: pas_pt_address
          schema: public
  - name: pas_pt_names
    using:
      foreign_key_constraint_on:
        column: patientid
        table:
          name: pas_pt_names
          schema: public
  - name: pas_pt_ur_numbers
    using:
      foreign_key_constraint_on:
        column: patientid
        table:
          name: pas_pt_ur_numbers
          schema: public
  - name: r_walktests_v1heavies
    using:
      foreign_key_constraint_on:
        column: patientid
        table:
          name: r_walktests_v1heavy
          schema: public
  - name: resmeds
    using:
      foreign_key_constraint_on:
        column: patientid
        table:
          name: resmed
          schema: public
  - name: rft_routines
    using:
      foreign_key_constraint_on:
        column: patientid
        table:
          name: rft_routine
          schema: public
insert_permissions:
  - role: user
    permission:
      check:
        site_id:
          _eq: X-Hasura-Site-Id
      set:
        site_id: x-hasura-Site-Id
      columns:
        - aboriginalstatus_code
        - countryofbirth_code
        - death_date
        - death_indicator
        - dob
        - email
        - gender_code
        - gender_forrfts_code
        - gpid_fromreftbl
        - lastupdated_by
        - lastupdated_date
        - medicare_expirydate
        - medicare_no
        - phone_home
        - phone_mobile
        - phone_work
        - preferredlanguage_code
        - race_code_notused
        - race_forrfts_code
        - research_discussed_by
        - research_discussed_date
        - research_discussed_outcome
        - research_notes
        - research_tags
        - ur
        - ur_hsid
        - ur_id
        - ur_mergeto
        - ur_mergeto_hsid
        - ur_mergeto_urid
    comment: ""
select_permissions:
  - role: user
    permission:
      columns:
        - aboriginalstatus_code
        - countryofbirth_code
        - death_date
        - death_indicator
        - email
        - gender_code
        - gender_forrfts_code
        - gpid_fromreftbl
        - lastupdated_by
        - medicare_expirydate
        - medicare_no
        - phone_home
        - phone_mobile
        - phone_work
        - preferredlanguage_code
        - race_code_notused
        - race_forrfts_code
        - research_discussed_by
        - research_discussed_outcome
        - ur
        - ur_hsid
        - ur_mergeto
        - ur_mergeto_hsid
        - dob
        - research_discussed_date
        - patientid
        - ur_id
        - ur_mergeto_urid
        - research_notes
        - research_tags
        - lastupdated_date
      filter:
        site_id:
          _eq: X-Hasura-Site-Id
      allow_aggregations: true
    comment: ""
update_permissions:
  - role: user
    permission:
      columns:
        - aboriginalstatus_code
        - countryofbirth_code
        - death_date
        - death_indicator
        - dob
        - email
        - gender_code
        - gender_forrfts_code
        - gpid_fromreftbl
        - lastupdated_by
        - lastupdated_date
        - medicare_expirydate
        - medicare_no
        - phone_home
        - phone_mobile
        - phone_work
        - preferredlanguage_code
        - race_code_notused
        - race_forrfts_code
        - research_discussed_by
        - research_discussed_date
        - research_discussed_outcome
        - research_notes
        - research_tags
        - ur
        - ur_hsid
        - ur_id
        - ur_mergeto
        - ur_mergeto_hsid
        - ur_mergeto_urid
      filter:
        site_id:
          _eq: X-Hasura-Site-Id
      check: {}
    comment: ""
event_triggers:
  - name: pas_pt
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
