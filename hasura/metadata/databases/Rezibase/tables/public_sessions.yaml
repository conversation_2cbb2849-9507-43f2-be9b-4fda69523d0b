table:
  name: sessions
  schema: public
object_relationships:
  - name: ab_user
    using:
      foreign_key_constraint_on: performed_by_id
  - name: control_methods_equipment
    using:
      foreign_key_constraint_on: control_method_equipment_id
array_relationships:
  - name: session_data
    using:
      foreign_key_constraint_on:
        column: session_id
        table:
          name: session_data
          schema: public
select_permissions:
  - role: user
    permission:
      columns:
        - control_method_equipment_id
        - id
        - performed_by_id
        - created
        - updated
        - session_time
      filter: {}
    comment: ""
event_triggers:
  - name: sessions
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
