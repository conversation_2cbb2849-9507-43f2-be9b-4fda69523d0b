table:
  name: list_permissiontypes
  schema: public
select_permissions:
  - role: user
    permission:
      columns:
        - enabled
        - category
        - code
        - datatype
        - defaultvalue
        - description
        - displaytext
        - helptext
        - tablename_list
        - permtypeid
      filter: {}
    comment: ""
event_triggers:
  - name: list_permissiontypes
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
