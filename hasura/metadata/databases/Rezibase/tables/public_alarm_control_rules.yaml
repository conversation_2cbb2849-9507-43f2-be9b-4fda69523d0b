table:
  name: alarm_control_rules
  schema: public
object_relationships:
  - name: alarm
    using:
      foreign_key_constraint_on: alarm_id
  - name: control_rule
    using:
      foreign_key_constraint_on: control_rule_id
select_permissions:
  - role: user
    permission:
      columns:
        - alarm_id
        - control_rule_id
        - id
      filter: {}
    comment: ""
event_triggers:
  - name: alarm_control_rules
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
