table:
  name: pred_ref_sources
  schema: public
object_relationships:
  - name: tests
    using:
      manual_configuration:
        column_mapping:
          id: sourceid
        insertion_order: null
        remote_table:
          name: pred_sourcextest
          schema: public
array_relationships:
  - name: ethnicities
    using:
      manual_configuration:
        column_mapping:
          id: sourceid
        insertion_order: null
        remote_table:
          name: pred_sourcexethnicity
          schema: public
  - name: genders
    using:
      manual_configuration:
        column_mapping:
          id: sourceid
        insertion_order: null
        remote_table:
          name: pred_sourcexgender
          schema: public
  - name: parameters
    using:
      manual_configuration:
        column_mapping:
          id: sourceid
        insertion_order: null
        remote_table:
          name: pred_sourcexparameter
          schema: public
insert_permissions:
  - role: user
    permission:
      check: {}
      columns:
        - description
        - lastedit
        - lasteditby
        - pub_reference
        - pub_year
        - source
        - sourceid
    comment: ""
select_permissions:
  - role: user
    permission:
      columns:
        - description
        - lasteditby
        - pub_reference
        - pub_year
        - source
        - id
        - sourceid
        - lastedit
      filter: {}
    comment: ""
update_permissions:
  - role: user
    permission:
      columns:
        - description
        - lastedit
        - lasteditby
        - pub_reference
        - pub_year
        - source
      filter: {}
      check: null
    comment: ""
event_triggers:
  - name: pred_ref_sources
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
