table:
  name: pred_equations
  schema: public
object_relationships:
  - name: prefs_pred_new
    using:
      manual_configuration:
        column_mapping:
          equationid: equationid
        insertion_order: null
        remote_table:
          name: prefs_pred_new
          schema: public
insert_permissions:
  - role: user
    permission:
      check: {}
      columns:
        - age_lower
        - age_upper
        - agegroup
        - agegroupid
        - equation_mpv
        - equation_range
        - equation_zscore
        - equationid
        - equationtype
        - equationtypeid
        - ethnicity
        - ethnicitycorrectiontype
        - ethnicitycorrectiontypeid
        - ethnicityid
        - gender
        - genderid
        - ht_lower
        - ht_upper
        - lastedit
        - lasteditby
        - parameter
        - parameterid
        - source
        - sourceid
        - stattype_range
        - stattype_rangeid
        - test
        - testid
        - wt_lower
        - wt_upper
    comment: ""
select_permissions:
  - role: user
    permission:
      columns:
        - agegroup
        - age_lower
        - age_upper
        - equation_mpv
        - equation_range
        - equationtype
        - equation_zscore
        - ethnicity
        - ethnicitycorrectiontype
        - gender
        - ht_lower
        - ht_upper
        - lasteditby
        - parameter
        - source
        - stattype_range
        - test
        - wt_lower
        - wt_upper
        - agegroupid
        - equationid
        - equationtypeid
        - ethnicitycorrectiontypeid
        - ethnicityid
        - genderid
        - id
        - parameterid
        - sourceid
        - stattype_rangeid
        - testid
        - lastedit
      filter: {}
      allow_aggregations: true
    comment: ""
update_permissions:
  - role: user
    permission:
      columns:
        - age_lower
        - age_upper
        - agegroup
        - agegroupid
        - equation_mpv
        - equation_range
        - equation_zscore
        - equationid
        - equationtype
        - equationtypeid
        - ethnicity
        - ethnicitycorrectiontype
        - ethnicitycorrectiontypeid
        - ethnicityid
        - gender
        - genderid
        - ht_lower
        - ht_upper
        - lastedit
        - lasteditby
        - parameter
        - parameterid
        - source
        - sourceid
        - stattype_range
        - stattype_rangeid
        - test
        - testid
        - wt_lower
        - wt_upper
      filter: {}
      check: {}
    comment: ""
delete_permissions:
  - role: user
    permission:
      filter: {}
    comment: ""
event_triggers:
  - name: pred_equations
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
