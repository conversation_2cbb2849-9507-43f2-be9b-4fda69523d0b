table:
  name: control_methods_equipments_parameters
  schema: public
object_relationships:
  - name: control_methods_equipment
    using:
      foreign_key_constraint_on: control_method_equipment_id
  - name: parameter
    using:
      foreign_key_constraint_on: parameter_id
array_relationships:
  - name: control_methods_equipments_parameters_control_rules
    using:
      foreign_key_constraint_on:
        column: control_method_equipment_parameter_id
        table:
          name: control_methods_equipments_parameters_control_rules
          schema: public
  - name: control_rule_intervals
    using:
      foreign_key_constraint_on:
        column: control_method_equipment_parameter_id
        table:
          name: control_rule_intervals
          schema: public
  - name: session_data
    using:
      foreign_key_constraint_on:
        column: control_method_equipment_parameter_id
        table:
          name: session_data
          schema: public
select_permissions:
  - role: user
    permission:
      columns:
        - control_method_equipment_id
        - id
        - parameter_id
        - created
        - updated
      filter: {}
    comment: ""
event_triggers:
  - name: control_methods_equipments_parameters
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
