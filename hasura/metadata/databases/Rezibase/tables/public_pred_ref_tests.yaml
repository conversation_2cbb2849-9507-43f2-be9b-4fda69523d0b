table:
  name: pred_ref_tests
  schema: public
array_relationships:
  - name: pred_ref_parameters
    using:
      foreign_key_constraint_on:
        column: testid
        table:
          name: pred_ref_parameters
          schema: public
  - name: pred_sourcextests
    using:
      foreign_key_constraint_on:
        column: testid
        table:
          name: pred_sourcextest
          schema: public
  - name: prefs_report_parameters
    using:
      foreign_key_constraint_on:
        column: testid
        table:
          name: prefs_report_parameters
          schema: public
select_permissions:
  - role: user
    permission:
      columns:
        - description
        - ganshorn_testtype
        - test
        - testid
      filter: {}
    comment: ""
event_triggers:
  - name: pred_ref_tests
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
