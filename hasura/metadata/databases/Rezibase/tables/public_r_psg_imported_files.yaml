table:
  name: r_psg_imported_files
  schema: public
object_relationships:
  - name: pas_pt
    using:
      foreign_key_constraint_on: patientid
select_permissions:
  - role: user
    permission:
      columns:
        - description
        - filename_original
        - filename_repo
        - filepath_original
        - filepath_repo
        - filetype
        - last_updated_by
        - loaded_by
        - report_status
        - unit
        - display_date
        - id
        - patientid
        - unitid
        - last_updated_date
        - loaded_date
        - display_time
      filter: {}
      allow_aggregations: true
    comment: ""
event_triggers:
  - name: r_psg_imported_files
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
