table:
  name: pred_sourcextest
  schema: public
object_relationships:
  - name: pred_ref_test
    using:
      foreign_key_constraint_on: testid
  - name: sources
    using:
      manual_configuration:
        column_mapping:
          sourceid: sourceid
        insertion_order: null
        remote_table:
          name: pred_ref_sources
          schema: public
insert_permissions:
  - role: user
    permission:
      check: {}
      columns:
        - sourceid
        - testid
    comment: ""
select_permissions:
  - role: user
    permission:
      columns:
        - id
        - sourceid
        - testid
      filter: {}
    comment: ""
event_triggers:
  - name: pred_sourcextest
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
