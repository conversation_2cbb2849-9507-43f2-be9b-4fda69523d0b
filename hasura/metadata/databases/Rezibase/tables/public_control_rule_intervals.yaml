table:
  name: control_rule_intervals
  schema: public
object_relationships:
  - name: control_methods_equipments_parameter
    using:
      foreign_key_constraint_on: control_method_equipment_parameter_id
select_permissions:
  - role: user
    permission:
      columns:
        - session_ids
        - mean_value
        - median_value
        - std_value
        - control_method_equipment_parameter_id
        - id
        - values_count
        - created
        - updated
      filter: {}
      allow_aggregations: true
    comment: ""
event_triggers:
  - name: control_rule_intervals
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
