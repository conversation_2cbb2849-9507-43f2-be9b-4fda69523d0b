table:
  name: r_hast
  schema: public
object_relationships:
  - name: pas_pt
    using:
      foreign_key_constraint_on: patientid
  - name: r_session
    using:
      foreign_key_constraint_on: sessionid
select_permissions:
  - role: user
    permission:
      columns:
        - bdstatus
        - deliverymethod_fio2
        - deliverymethod_suppo2
        - lab
        - lastupdatedby_hast
        - protocol_name
        - report_amendedby
        - report_authorisedby
        - report_reportedby
        - report_status
        - report_verifiedby
        - scientist
        - testtype
        - report_amendeddate
        - report_authoriseddate
        - report_reporteddate
        - report_verifieddate
        - hastid
        - patientid
        - protocol_id
        - sessionid
        - report_amendednotes
        - report_text
        - technicalnotes
        - lastupdated_hast
        - testtime
      filter: {}
      allow_aggregations: true
    comment: ""
event_triggers:
  - name: r_hast
    definition:
      delete:
        columns: '*'
      enable_manual: false
      insert:
        columns: '*'
      update:
        columns: '*'
    retry_conf:
      interval_sec: 10
      num_retries: 0
      timeout_sec: 60
    webhook: '{{HASURA_WEBHOOK_URL}}'
