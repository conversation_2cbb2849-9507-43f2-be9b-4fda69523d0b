from flask_jwt_extended import create_access_token, get_jwt

from .models import User


def create_user_access_token(user: User, site_id=None, mfa_required=None):
    claims = {
        "X-Hasura-Default-Role": "user",
        "X-Hasura-Allowed-Roles": ["user"],
        "X-Hasura-Allowed-Sites": "{" + ",".join(str(site.id) for site in user.sites) + "}",
        'X-Hasura-User-Id': str(user.id),
    }
    try:
        current_token = get_jwt()
    except Exception:
        current_token = {}
    site_id = site_id or current_token.get('site_id', None)

    if site_id:
        claims["X-Hasura-Site-Id"] = str(site_id)
    elif len(user.sites) > 0:
        claims["X-Hasura-Site-Id"] = str(user.sites[0].id)

    # Set MFA required status
    if mfa_required is None:
        mfa_required = current_token.get('mfaRequired', user.totp_enabled)

    additional_claims = {
        "https://hasura.io/jwt/claims": claims,
        "mfaRequired": mfa_required
    }

    return create_access_token(
        identity=str(user.id),
        additional_claims=additional_claims
    )


def get_current_site_id():
    """
    Helper function to extract site ID from JWT
    """
    jwt_claims = get_jwt()
    hasura_claims = jwt_claims.get('https://hasura.io/jwt/claims', {})
    return hasura_claims.get('X-Hasura-Site-Id')