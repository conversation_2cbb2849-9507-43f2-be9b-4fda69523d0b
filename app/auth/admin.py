import re
import secrets
import uuid

import regex as regex
from flask import session, render_template, request, flash, redirect, url_for
from flask_admin import expose
from flask_mail import Message
from flask_security import hash_password
from wtforms import ValidationError, Form
from wtforms.fields import <PERSON>ail<PERSON>ield, <PERSON><PERSON>ield, SelectMultipleField
from wtforms.validators import DataRequired, Email, InputRequired

from app.auth.models import Permission, Role, UserSites
from app.utils.models import Site
from common import TrialModelView, mail
from config import Config


class InviteUserForm(Form):
    email = EmailField(
        'Email Address',
        validators=[DataRequired(message="Email is required"), Email(message="Invalid email address")],
        render_kw={"placeholder": "Enter user's email"}
    )
    first_name = StringField(validators=[InputRequired()], render_kw={"placeholder": "Enter user's first name"})
    last_name = <PERSON><PERSON><PERSON>(validators=[InputRequired()], render_kw={"placeholder": "Enter user's last name"})
    roles = SelectMultipleField(
        'Roles',
        coerce=int,
        render_kw={"placeholder": "Select roles for the user"}
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Dynamically populate role choices
        from app.auth.models import Role
        self.roles.choices = [(role.id, role.name) for role in Role.query.order_by(Role.name).all()]


class PermissionAdminView(TrialModelView):
    column_list = ('code', 'display_text', 'help_text', 'created_at', 'updated_at')
    form_columns = ('code', 'display_text', 'help_text')
    column_searchable_list = ('code', 'display_text', 'help_text')
    column_filters = ('code', 'display_text')
    column_labels = {'code': 'Code', 'display_text': 'Display Text', 'help_text': 'Help Text'}
    form_widget_args = {'help_text': {'rows': 3}}

    def __init__(self, session, **kwargs):
        super(PermissionAdminView, self).__init__(Permission, session, name="Permissions", **kwargs)


class RoleAdminView(TrialModelView):
    column_list = ('name', 'description', 'permissions')
    form_columns = ('name', 'description', 'permissions')
    column_searchable_list = ('name', 'description')
    column_filters = ('name',)

    def __init__(self, session, **kwargs):
        super(RoleAdminView, self).__init__(Role, session, name="Roles", **kwargs)


class UserAdmin(TrialModelView):
    def password_validator(form, field):
        if not bool(re.search(regex.get('digits'), field)):
            raise ValidationError('Field must contain one digit')
        if not bool(re.search(regex.get('letters'), field)):
            raise ValidationError('Field must contain one letter')
        if not bool(re.search(regex.get('symbols'), field)) and bool(re.search(regex.get('spaces'), field)):
            raise ValidationError('Field must contains symbols or spaces')

        rules = [
            lambda s: any(x.isupper() for x in s) or 'upper',
            lambda s: any(x.islower() for x in s) or 'lower',
        ]

        problems = [p for p in [r(field) for r in rules] if p is not True]
        for p in problems:
            raise ValidationError(f'Field must contain one {p} case character')

    page_size = 20  # the number of entries to display on the list view
    column_default_sort = 'name'
    column_list = ('name', 'email', 'roles', 'confirmed_at', 'sites', 'active', 'totp_enabled')
    column_sortable_list = ('name', 'email', 'confirmed_at', 'totp_enabled')

    column_filters = ['email', 'first_name', 'last_name', 'active']
    col_width = {'name': 'width: 116.25px;'}
    column_searchable_list = column_filters
    can_export = True
    can_create = False
    form_columns = ('first_name', 'last_name', 'email',
                    'password', 'roles', 'sites', 'active', 'direct_permissions','totp_enabled')
    form_choices = {
        'gender': [
            ('male', 'Male'), ('female', 'Female'), ('other', 'Other')
        ]
    }

    def _confirmed_at(view, context, model, name):
        if model.confirmed_at:
            return model.confirmed_at.strftime("%d-%m-%Y")
        else:
            return ""

    column_formatters = {
        'confirmed_at': _confirmed_at
    }

    def on_model_change(self, form, model, is_created):
        if is_created:
            model.fs_uniquifier = str(uuid.uuid4())

    def get_query(self):
        return super().get_query().join(UserSites).filter(
            UserSites.site_id == session.get('site_id')
        )

    def get_count_query(self):
        return super().get_count_query().join(UserSites).filter(
            UserSites.site_id == session.get('site_id')
        )

    @expose('/invite/', methods=('GET', 'POST'))
    def invite_user_view(self):
        form = InviteUserForm(request.form)
        form_opts = {}

        if request.method == 'POST' and form.validate():
            email = form.email.data
            site_id = session.get('site_id')

            if not site_id:
                flash('Current site not found in session. Cannot invite user.', 'error')
                return redirect(self.get_url('.index_view'))

            site = self.session.query(Site).filter_by(id=site_id).first()
            if not site:
                flash(f'Site with ID {site_id} not found. Cannot proceed with invitation.', 'error')
                return self.render('admin/user/invite.html', form=form, form_opts=form_opts)

            site_identifier = site.name

            user = self.session.query(self.model).filter_by(email=email).first()
            new_user_created = False
            password_for_email = None

            if not user:
                try:
                    password = secrets.token_urlsafe(12)
                    password_for_email = password

                    user_data = {
                        **form.data,
                        'password': hash_password(password),
                        'active': True,
                        'meta': {
                            'password_reset_required': True
                        }
                    }
                    if hasattr(self.model, 'fs_uniquifier'):
                        user_data['fs_uniquifier'] = str(uuid.uuid4())

                    # Remove roles from user_data, will assign after user is created
                    user_data.pop('roles', None)

                    user = self.model(**user_data)

                    self.session.add(user)
                    self.session.commit()
                    new_user_created = True
                    flash(f'New user {email} created. A temporary password will be sent to them.', 'success')

                    # Assign selected roles to the user
                    selected_role_ids = form.roles.data
                    if selected_role_ids:
                        from app.auth.models import Role
                        roles = self.session.query(Role).filter(Role.id.in_(selected_role_ids)).all()
                        user.roles = roles
                        self.session.commit()
                except Exception as e:
                    self.session.rollback()
                    flash(f'Error creating user {email}: {str(e)}', 'error')
                    return self.render('admin/user/invite.html', form=form, form_opts=form_opts)
            else:
                flash(f'User {email} already exists.', 'info')

            # Add user to current site if they exist (either newly created or pre-existing)
            if user:
                user_site_exists = self.session.query(UserSites).filter_by(user_id=user.id, site_id=site_id).first()
                if not user_site_exists:
                    try:
                        user_site = UserSites(user_id=user.id, site_id=site_id)
                        self.session.add(user_site)
                        self.session.commit()
                        flash(f'User {email} has been added to the current site.', 'success')
                    except Exception as e:
                        self.session.rollback()
                        flash(f'Error adding user {email} to site: {str(e)}', 'error')
                        return self.render('admin/user/invite.html', form=form, form_opts=form_opts)
                else:
                    flash(f'User {email} is already associated with this site.', 'info')
                    return redirect(self.get_url('.index_view'))

                email_context = {
                    'email': email,
                    'site_identifier': site_identifier,
                    'application_name': "Rezibase",
                    'login_url': url_for('home_bp.catch_all', site_id=site_id, _external=True).replace('/?', '/auth/login?'),
                    'dashboard_url': url_for('home_bp.catch_all', site_id=site_id, _external=True)
                }

                if new_user_created and password_for_email:
                    email_context['password_for_email'] = password_for_email
                    subject = render_template("admin/email/new_user_invite_subject.txt", **email_context)
                    body = render_template("admin/email/new_user_invite_body.txt", **email_context)
                    try:
                        msg = Message(
                            subject=subject,
                            sender=Config.MAIL_DEFAULT_SENDER,  # Using configured default sender
                            recipients=[email],
                            body=body
                        )
                        mail.send(msg)
                        flash(f'Invitation email sent to {email}.', 'success')
                    except Exception as e:
                        flash(f'Error sending invitation email to {email}: {str(e)}', 'danger')
                        # Log the error for debugging: current_app.logger.error(f"Email send error: {e}")
                elif user:
                    subject = render_template("admin/email/existing_user_notification_subject.txt", **email_context)
                    body = render_template("admin/email/existing_user_notification_body.txt", **email_context)
                    try:
                        msg = Message(
                            subject=subject,
                            sender=Config.MAIL_DEFAULT_SENDER,
                            recipients=[email],
                            body=body
                        )
                        mail.send(msg)
                        flash(f'Notification email sent to {email} about site access.', 'success')
                    except Exception as e:
                        flash(f'Error sending notification email to {email}: {str(e)}', 'danger')
                        # Log the error for debugging: current_app.logger.error(f"Email send error: {e}")

            return redirect(self.get_url('.index_view'))

        return self.render('admin/user/invite.html', form=form, form_opts=form_opts)

    @property
    def _template_args(self):
        args = super()._template_args if hasattr(super(), '_template_args') else {}
        args['custom_list_actions'] = [
            {
                'name': 'Invite User',
                'url': self.get_url('.invite_user_view'),
                'icon_class': 'flaticon2-plus',
                'class': 'btn-primary'
            }
        ]
        return args

    def get_edit_form(self):
        form_class = super(UserAdmin, self).get_edit_form()

        del form_class.password
        return form_class
