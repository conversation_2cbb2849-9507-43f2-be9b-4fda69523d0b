from flask import Blueprint, request, jsonify
from .models import ActivityLog
from datetime import datetime

activity_logs_bp = Blueprint('activity_logs_bp', __name__)

@activity_logs_bp.route('/webhook/activity', methods=['POST'])
def activity_webhook():
    try:
        payload = request.json

        event = payload.get('event', {})
        operation = event.get('op')
        table_info = payload.get('table', {})
        table_name = table_info.get('name')
        data = event.get('data', {})

        session_vars = event.get('session_variables', {})
        user_id = None
        if session_vars.get('x-hasura-user-id'):
            try:
                user_id = int(session_vars['x-hasura-user-id'])
            except (ValueError, TypeError):
                pass

        record_id = None
        if operation in ['INSERT', 'UPDATE']:
            new_data = data.get('new', {})
            record_id = new_data.get('id')
        elif operation == 'DELETE':
            old_data = data.get('old', {})
            record_id = old_data.get('id')

        ip_address = request.headers.get('X-Forwarded-For', request.remote_addr)
        details = {
            'hasura_payload': payload,
            'event': {
                'operation': operation,
                'table': {
                    'name': table_name,
                    'schema': table_info.get('schema', 'public')
                },
                'trigger_name': payload.get('trigger', {}).get('name'),
                'hasura_event_id': payload.get('id'),
                'timestamp': payload.get('created_at')
            },

            'data_changes': data,
            'user_context': session_vars,
            'request_metadata': {
                'ip_address': ip_address,
                'user_agent': request.headers.get('User-Agent'),
                'content_type': request.headers.get('Content-Type'),
                'headers': dict(request.headers),
                'processed_at': datetime.utcnow().isoformat(),
                'webhook_version': '1.0'
            },

            'delivery_info': payload.get('delivery_info', {}),
            'trace_context': event.get('trace_context', {})
        }

        if operation == 'UPDATE':
            old_data = data.get('old', {})
            new_data = data.get('new', {})
            changed_fields = {}

            for key, new_value in new_data.items():
                old_value = old_data.get(key)
                if new_value != old_value:
                    changed_fields[key] = {
                        'old': old_value,
                        'new': new_value
                    }

            details['change_summary'] = {
                'fields_changed': list(changed_fields.keys()),
                'changes_count': len(changed_fields),
                'changed_fields': changed_fields
            }

        if not operation:
            return jsonify({'success': False, 'error': 'Missing operation'}), 400
        if not table_name:
            return jsonify({'success': False, 'error': 'Missing table name'}), 400

        activity_log = ActivityLog.create_log(
            user_id=user_id,
            action_type=operation,
            table_name=table_name,
            record_id=record_id,
            details=details,
            ip_address=ip_address
        )

        return jsonify({
            'success': True,
            'message': 'Activity logged successfully',
            'log_id': activity_log.id,
            'captured': {
                'operation': operation,
                'table': table_name,
                'record_id': record_id,
                'user_id': user_id,
                'data_size': len(str(details))
            }
        }), 200

    except Exception as e:
        print(f"Webhook Error: {str(e)}")
        print(f"Request payload: {request.json}")

        return jsonify({
            'success': False,
            'error': 'Internal webhook error',
            'message': str(e)
        }), 500


@activity_logs_bp.route('/webhook/health', methods=['GET'])
def webhook_health():
    """Health"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.utcnow().isoformat()
    }), 200