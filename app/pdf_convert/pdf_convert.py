from app.utils.models import SiteSettings
from config import Config
import base64
import fitz  # PyMuPDF
import pdfplumber
from flask import current_app
import tempfile
import os
import re
from collections import defaultdict
from datetime import datetime
from app import db
from app.pdf_convert.models import PasPt, PasPtName, RSession, RftRoutine, DeviceTemplate, AttachmentFile
from flask import Blueprint, request, jsonify
import boto3



file_bp = Blueprint(
    "file_bp", __name__, template_folder="templates", static_folder="static"
)

# ----- RFT Measurements Extraction -----
def parse_ganshorn_measurements(lines):
    measurements = defaultdict(lambda: {"pre": "", "post": ""})
    row_map = {
        "FEV 1 [L]": "fev_1", "FVC [L]": "fvc", "FEF 25-75 [L/s]": "fef_25_75",
        "PEF [L/s]": "pef", "VC MAX [L]": "vc_max", "DLCO [ml/min/mmHg]": "dlco",
        "VA [L]": "va", "Hb [g/dl]": "hb", "VC IN [L]": "vc_in",
        "PI max [kPa]": "pi_max", "PE max [kPa]": "pe_max", "TLC [L]": "tlc",
        "FRC [L]": "frc", "RV [L]": "rv", "ERV [L]": "erv", "IC [L]": "ic"
    }

    current_section = None
    for line in lines:
        line = line.strip()
        if line == "Pred Pre":
            current_section = "pre"
            continue
        elif line == "Pred Post":
            current_section = "post"
            continue

        for key in row_map:
            if line.startswith(key):
                values = re.findall(r"[-+]?\d*\.\d+|\d+", line[len(key):].strip())
                short_key = row_map[key]
                if current_section and len(values) >= 2:
                    # Use the second number (actual measured value)
                    measurements[short_key][current_section] = values[1]
                break
    return measurements
def parse_breeze_measurements(lines):
    result = {}

    row_map = {
        "FEV1 (L)": "fev_1",
        "FVC (L)": "fvc",
        "FEF 25-75% (L/sec)": "fef_25_75",
        "PEF (L/min)": "pef",
        "SVC (L)": "svc",
        "DLCOunc (ml/min/mmHg)": "dlco",
        "VA (L)": "va",
        "IVC (L)": "ivc",
        "ERV (L)": "erv",
        "IC (L)": "ic"
    }

    for line in lines:
        line = line.strip()
        for label, key in row_map.items():
            if line.startswith(label):
                value_match = re.search(r"[-+]?\d*\.\d+|\d+", line[len(label):])
                result[key] = value_match.group(0) if value_match else ""
                break

    return result


def extract_graph_image(pdf_path, page_index, params):
    # Extract parameters with defaults
    dpi = params.get("dpi", 96)
    base_dpi = params.get("base_dpi", 72)
    cm_per_inch = 2.54
    cm_to_points = base_dpi / cm_per_inch

    # Margins and graph dimensions in cm
    margin_left_cm = params.get("margin_left_cm", 0.5)
    margin_top_cm = params.get("margin_top_cm", 3.0)
    graph_width_cm = params.get("graph_width_cm", 13.0)
    graph_height_cm = params.get("graph_height_cm", 14.0)

    # Convert cm to points
    margin_left = margin_left_cm * cm_to_points
    margin_top = margin_top_cm * cm_to_points
    graph_width = graph_width_cm * cm_to_points
    graph_height = graph_height_cm * cm_to_points

    # Define clipping rectangle
    rect = fitz.Rect(
        margin_left,
        margin_top,
        margin_left + graph_width,
        margin_top + graph_height
    )

    # Load page and render clipped image
    doc = fitz.open(pdf_path)
    page = doc.load_page(page_index)
    mat = fitz.Matrix(dpi / base_dpi, dpi / base_dpi)
    pix = page.get_pixmap(matrix=mat, clip=rect)
    image_bytes = pix.tobytes("png")
    doc.close()

    # Return base64-encoded PNG image
    return base64.b64encode(image_bytes).decode("utf-8")


def extract_valid_float(value):
    match = re.search(r'\d+(\.\d+)?', value)
    return match.group(0) if match else value.strip()


def parse_patient_info(text):
    patient_info = {}
    lines = text.split('\n')
    template = 'ganshorn'
    for i, line in enumerate(lines):
        line = line.strip()
        print(line)  # Debugging line to check the content of each line
        if line.startswith("Last name"):
            patient_info["last_name"] = line[len("Last name"):].strip()
        elif line.startswith("First name"):
            patient_info["first_name"] = line[len("First name"):].strip()
        elif line.startswith("Date of birth"):
            patient_info["date_of_birth"] = line[len("Date of birth"):].strip()
        elif line.startswith("Patient Id"):
            patient_info["patient_id"] = line[len("Patient Id"):].strip()
            patient_info["age"] = line[len("Age"):].strip()
        elif line.startswith("Age"):
            patient_info["age"] = line[len("Age"):].strip()
        elif line.startswith("Height"):
            patient_info["height"] = extract_valid_float(line[len("Height"):].strip())
        elif line.startswith("Weight"):
            patient_info["weight"] = extract_valid_float(line[len("Weight"):].strip())
        elif line.startswith("Gender"):
            patient_info["gender"] = line[len("Gender"):].strip()
        elif line.startswith("Ethnic group"):
            patient_info["ethnic_group"] = line[len("Ethnic group"):].strip()
        elif line.startswith("Smoker"):
            patient_info["smoker"] = line[len("Smoker"):].strip()
        elif line.startswith("Pack years"):
            patient_info["pack_years"] = line[len("Pack years"):].strip()
        elif line.startswith("Pre:"):
            match = re.search(r"(\d{1,2}/\d{1,2}/\d{4})\s+(\d{1,2}:\d{2}\s*[AP]M)", line)
            if match:
                try:
                    date_time_str = f"{match.group(1)} {match.group(2)}"
                    dt_obj = datetime.strptime(date_time_str, "%d/%m/%Y %I:%M %p")
                    patient_info["test_date"] = dt_obj.date().isoformat()  # e.g., '2025-01-13'
                    patient_info["visit_time"] = dt_obj.time().isoformat()  # e.g., '16:15:00'
                except ValueError:
                    print(f"[Warning] Invalid date format in line: {line}")
        elif line.startswith("VisitDate"):
            date_str = line[len("VisitDate"):].strip()
            try:
                date_obj = datetime.strptime(date_str, "%d/%m/%Y").date()
                patient_info["test_date"] = date_obj.isoformat()
            except ValueError:
                print(f"[Warning] Invalid date format in line: {line}")
        elif line.startswith("VisitTime"):
            patient_info["visit_time"] = line[len("VisitTime"):].strip()
        elif line.startswith("Test Type"):
            if line[len("Test Type"):].strip() == "RFT":
                patient_info["test_type"] = line[len("Test Type"):].strip()
                template = 'breeze'
    return patient_info, template
def will_overwrite_existing_data(rft_routine, rft_measurements):
    """Returns True if any existing data will be overwritten by the new measurements or image."""
    fields_to_check = {
        'r_bl_tlc': rft_measurements.get("tlc", {}).get("pre"),
        'r_bl_frc': rft_measurements.get("frc", {}).get("pre"),
        'r_bl_rv': rft_measurements.get("rv", {}).get("pre"),
        'r_bl_erv': rft_measurements.get("erv", {}).get("pre"),
        'r_bl_ic': rft_measurements.get("ic", {}).get("pre"),
        'r_bl_vc': rft_measurements.get("vc_max", {}).get("pre"),
        'r_bl_fev1': rft_measurements.get("fev_1", {}).get("pre"),
        'r_bl_fvc': rft_measurements.get("fvc", {}).get("pre"),
        'r_bl_fef2575': rft_measurements.get("fef_25_75", {}).get("pre"),
        'r_bl_pef': rft_measurements.get("pef", {}).get("pre"),
        'r_bl_tlco': rft_measurements.get("dlco", {}).get("pre"),
        'r_bl_va': rft_measurements.get("va", {}).get("pre"),
        'r_bl_hb': rft_measurements.get("hb", {}).get("pre"),
        'r_bl_ivc': rft_measurements.get("vc_in", {}).get("pre"),
        'r_bl_mip': rft_measurements.get("pi_max", {}).get("pre"),
        'r_bl_mep': rft_measurements.get("pe_max", {}).get("pre"),
    }

    for field, new_value in fields_to_check.items():
        current_value = getattr(rft_routine, field)
        if current_value not in (None, '') and new_value not in (None, ''):
            return True
    return False

@file_bp.route('/api/pdf_import/patient', methods=['POST'])
def pdf_import_patient():
    try:
        print("Processing PDF import for patient...")
        if 'file' not in request.files:
            return jsonify({'error': 'No file uploaded'}), 400
        file = request.files['file']
        rft_id = request.form.get('id')
        patient_id = request.form.get('patientId')

        is_overwrite = request.form.get('is_overwrite', 'false').lower() == 'true'

        if not patient_id or not patient_id.strip():
            return jsonify({'error': 'Invalid Patient'}), 400

        if file.filename == '':
            return jsonify({'error': 'No selected file'}), 400

        with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as tmp:
            file.save(tmp.name)
            tmp_path = tmp.name

        with pdfplumber.open(tmp_path) as pdf_doc:
            page_texts = [p.extract_text() for p in pdf_doc.pages if p.extract_text()]

        patient_info, template = parse_patient_info(page_texts[0])
        matched_patient = PasPt.query.filter(PasPt.patientid == patient_id).first()
        print(f"Matched patient: {patient_id}")

        dob = datetime.strptime(patient_info["date_of_birth"], "%d/%m/%Y").date()
        imported_patient = PasPt.query.join(PasPtName).filter(
            PasPt.dob == dob,
            PasPtName.firstname.ilike(patient_info["first_name"]),
            PasPtName.surname.ilike(patient_info["last_name"])
        ).first()

        if not matched_patient:
            return jsonify({'error': 'Patient not found'}), 404


        # Measurement parsing and image extraction
        if template == 'ganshorn':
            rft_measurements = parse_ganshorn_measurements(page_texts[1].splitlines()) if len(page_texts) > 1 else {}
            params = {
                "dpi": 150,
                "base_dpi": 72,
                "margin_left_cm": 1.0,
                "margin_top_cm": 2.5,
                "graph_width_cm": 12.0,
                "graph_height_cm": 10.0
            }
            image_base64 = extract_graph_image(tmp_path, 2, params)
        else:
            pre = parse_breeze_measurements(page_texts[1].splitlines()) if len(page_texts) > 2 else {}
            post = parse_breeze_measurements(page_texts[2].splitlines()) if len(page_texts) > 2 else {}

            rft_measurements = defaultdict(lambda: {"pre": "", "post": ""})
            for key, val in pre.items():
                rft_measurements[key]["pre"] = val
            for key, val in post.items():
                rft_measurements[key]["post"] = val

            params = {
                "dpi": 150,
                "base_dpi": 72,
                "margin_left_cm": 1.0,
                "margin_top_cm": 7.0,
                "graph_width_cm": 19.0,
                "graph_height_cm": 24.0
            }

            image_base64 = extract_graph_image(tmp_path, 3, params)

        # Save measurements to DB
        rft_routine = RftRoutine.query.filter_by(rftid=int(rft_id)).first()
        if not rft_routine:
            return jsonify({'error': 'RFT record not found'}), 404
        overwrite_data_flag = will_overwrite_existing_data(rft_routine, rft_measurements)
        if not imported_patient or imported_patient.patientid != matched_patient.patientid:
            if not is_overwrite:
                if overwrite_data_flag:
                    return jsonify({
                        'error': 'Patient mismatch',
                        'message': 'Patient data does not match and will update existing form data. Do you want to '
                                   'overwrite the existing patient data?'
                    }), 200
                return jsonify({
                    'error': 'Patient mismatch',
                    'message': 'Patient data does not match. Do you want to overwrite the existing patient data?'
                }), 200
        if overwrite_data_flag and not is_overwrite:
            return jsonify({
                'error': 'Data Overwrite Warning',
                'message': 'The existing data will be overwritten. Do you want to proceed?'
            }), 200
        existing_data = {
            "r_bl_tlc": rft_routine.r_bl_tlc,
            "r_bl_frc": rft_routine.r_bl_frc,
            "r_bl_rv": rft_routine.r_bl_rv,
            "r_bl_erv": rft_routine.r_bl_erv,
            "r_bl_ic": rft_routine.r_bl_ic,
            "r_bl_vc": rft_routine.r_bl_vc,
            "r_bl_fev1": rft_routine.r_bl_fev1,
            "r_bl_fvc": rft_routine.r_bl_fvc,
            "r_bl_fef2575": rft_routine.r_bl_fef2575,
            "r_bl_pef": rft_routine.r_bl_pef,
            "r_bl_tlco": rft_routine.r_bl_tlco,
            "r_bl_va": rft_routine.r_bl_va,
            "r_bl_hb": rft_routine.r_bl_hb,
            "r_bl_ivc": rft_routine.r_bl_ivc,
            "r_bl_mip": rft_routine.r_bl_mip,
            "r_bl_mep": rft_routine.r_bl_mep,
            "flowvolloop": base64.b64encode(rft_routine.flowvolloop).decode() if rft_routine.flowvolloop else None
        }
        rft_routine.flowvolloop = base64.b64decode(image_base64)
        rft_routine.r_bl_tlc = rft_measurements.get("tlc", {}).get("pre")
        rft_routine.r_bl_frc = rft_measurements.get("frc", {}).get("pre")
        rft_routine.r_bl_rv = rft_measurements.get("rv", {}).get("pre")
        rft_routine.r_bl_erv = rft_measurements.get("erv", {}).get("pre")
        rft_routine.r_bl_ic = rft_measurements.get("ic", {}).get("pre")
        rft_routine.r_bl_vc = rft_measurements.get("vc_max", {}).get("pre")
        rft_routine.r_bl_fev1 = rft_measurements.get("fev_1", {}).get("pre")
        rft_routine.r_bl_fvc = rft_measurements.get("fvc", {}).get("pre")
        rft_routine.r_bl_fef2575 = rft_measurements.get("fef_25_75", {}).get("pre")
        rft_routine.r_bl_pef = rft_measurements.get("pef", {}).get("pre")
        rft_routine.r_bl_tlco = rft_measurements.get("dlco", {}).get("pre")
        rft_routine.r_bl_va = rft_measurements.get("va", {}).get("pre")
        rft_routine.r_bl_hb = rft_measurements.get("hb", {}).get("pre")
        rft_routine.r_bl_ivc = rft_measurements.get("vc_in", {}).get("pre")
        rft_routine.r_bl_mip = rft_measurements.get("pi_max", {}).get("pre")
        rft_routine.r_bl_mep = rft_measurements.get("pe_max", {}).get("pre")

        db.session.commit()

        # Save to S3
        new_file = upload_file_to_s3(file, patient_id, rft_id,matched_patient.site_id)
        return jsonify({'success': True, 'original': existing_data})
    except Exception as e:
        print(f"Error processing PDF: {e}")
        return jsonify({'error': str(e)}), 500

    finally:
        if 'tmp_path' in locals() and os.path.exists(tmp_path):
            os.remove(tmp_path)

@file_bp.route('/api/pdf_import/undo', methods=['POST'])
def undo_pdf_import():
    try:
        data = request.get_json()
        rft_id = data.get('rft_id')
        original = data.get('original')
        print(original)

        if not rft_id or not original:
            return jsonify({'error': 'Missing required data'}), 400

        rft_routine = RftRoutine.query.filter_by(rftid=rft_id).first()
        if not rft_routine:
            return jsonify({'error': 'RFT record not found'}), 404

        # Manually set each field
        rft_routine.r_bl_tlc = original.get("r_bl_tlc")
        rft_routine.r_bl_frc = original.get("r_bl_frc")
        rft_routine.r_bl_rv = original.get("r_bl_rv")
        rft_routine.r_bl_erv = original.get("r_bl_erv")
        rft_routine.r_bl_ic = original.get("r_bl_ic")
        rft_routine.r_bl_vc = original.get("r_bl_vc")
        rft_routine.r_bl_fev1 = original.get("r_bl_fev1")
        rft_routine.r_bl_fvc = original.get("r_bl_fvc")
        rft_routine.r_bl_fef2575 = original.get("r_bl_fef2575")
        rft_routine.r_bl_pef = original.get("r_bl_pef")
        rft_routine.r_bl_tlco = original.get("r_bl_tlco")
        rft_routine.r_bl_va = original.get("r_bl_va")
        rft_routine.r_bl_hb = original.get("r_bl_hb")
        rft_routine.r_bl_ivc = original.get("r_bl_ivc")
        rft_routine.r_bl_mip = original.get("r_bl_mip")
        rft_routine.r_bl_mep = original.get("r_bl_mep")

        flowvolloop_encoded = original.get("flowvolloop")
        if flowvolloop_encoded:
            rft_routine.flowvolloop = base64.b64decode(flowvolloop_encoded)
        else:
            rft_routine.flowvolloop = None

        db.session.commit()
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@file_bp.route('/api/pdf_import', methods=['POST'])
def pdf_import():

    if 'file' not in request.files:
        return jsonify({'error': 'No file uploaded'}), 400

    file = request.files['file']
    site_id = request.form.get('site_id', None)
    if file.filename == '':
        return jsonify({'error': 'No selected file'}), 400

    with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as tmp:
        file.save(tmp.name)
        tmp_path = tmp.name

    try:
        with pdfplumber.open(tmp_path) as pdf_doc:
            page_texts = [p.extract_text() for p in pdf_doc.pages if p.extract_text()]
        patient_info, template = parse_patient_info(page_texts[0])
        if template == 'ganshorn':
            rft_measurements = parse_ganshorn_measurements(page_texts[1].splitlines()) if len(page_texts) > 1 else {}
            params = {
                "dpi": 150,
                "base_dpi": 72,
                "margin_left_cm": 1.0,
                "margin_top_cm": 2.5,
                "graph_width_cm": 12.0,
                "graph_height_cm": 10.0
            }
            image_base64 = extract_graph_image(tmp_path, 2, params)
        else:
            pre = parse_breeze_measurements(page_texts[1].splitlines()) if len(page_texts) > 2 else {}
            post = parse_breeze_measurements(page_texts[2].splitlines()) if len(page_texts) > 2 else {}

            rft_measurements = defaultdict(lambda: {"pre": "", "post": ""})
            for key, val in pre.items():
                rft_measurements[key]["pre"] = val
            for key, val in post.items():
                rft_measurements[key]["post"] = val
            params = {
                "dpi": 150,  # Higher resolution for better clarity
                "base_dpi": 72,
                "margin_left_cm": 1.0,  # More left margin to account for white space
                "margin_top_cm": 7.0,  # Increased top margin to match vertical offset
                "graph_width_cm": 19.0,  # Slightly wider to capture full X-axis labels
                "graph_height_cm": 24.0  # Taller to ensure entire graph is included
            }

            image_base64 = extract_graph_image(tmp_path, 3, params)
        os.remove(tmp_path)

        # Match patient
        matched_patient = None
        match_query = PasPt.query

        ur = patient_info.get("patient_id")
        if ur:
            match_query = match_query.filter(PasPt.ur == ur,PasPt.site_id ==site_id)
        elif all(k in patient_info for k in ("first_name", "last_name", "date_of_birth")):
            try:
                dob = datetime.strptime(patient_info["date_of_birth"], "%d/%m/%Y").date()
                match_query = match_query.join(PasPtName).filter(
                    PasPt.dob == dob,
                    PasPtName.firstname.ilike(patient_info["first_name"]),
                    PasPtName.surname.ilike(patient_info["last_name"])
                )
            except ValueError:
                pass  # Invalid DOB format, no match

        matched = match_query.first()
        if matched:
            matched_name = PasPtName.query.filter_by(patientid=matched.patientid).first()
            matched_patient = {
                "patient_id": matched.patientid,
                "ur": matched.ur,
                "first_name": matched_name.firstname if matched_name else None,
                "last_name": matched_name.surname if matched_name else None,
                "dob": matched.dob.strftime('%d/%m/%Y') if matched.dob else None,
                "gender": matched.gender_code,
            }

        return jsonify({
            "patient_info": patient_info,
            "rft_measurements": rft_measurements,
            "image_base64": image_base64,
            "matched_patient": matched_patient
        })

    except Exception as e:
        if os.path.exists(tmp_path):
            os.remove(tmp_path)
        print(f"Error processing PDF: {e}")
        return jsonify({'error': str(e)}), 500
@file_bp.route('/api/pdf_import/confirm', methods=['POST'])
def pdf_import_confirm():
    data = request.get_json()
    overwrite = data.get("overwrite", False)
    site_id = str(data.get("site_id"))
    patient_info = data.get("patient_info", {})
    measurements = data.get("rft_measurements", {})
    image_base64 = data.get("image_base64")
    create_new = data.get("create_new", False)
    patient_id = str(data.get("patient_id")) if data.get("patient_id") else None
    if not patient_info:
        return jsonify({"error": "Missing patient_info"}), 400

    # Attempt to match patient
    paspt = None
    dob = None
    ur = patient_info.get("patient_id") if not patient_id else patient_id
    first_name = patient_info.get("first_name")
    last_name = patient_info.get("last_name")
    raw_dob = patient_info.get("date_of_birth")

    if raw_dob:
        try:
            dob = datetime.strptime(raw_dob, "%d/%m/%Y").date()
        except ValueError:
            return jsonify({"error": "Invalid DOB format"}), 400

    if ur:
        paspt = PasPt.query.filter_by(ur=ur).first()

    if not paspt and first_name and last_name and dob:
        paspt = PasPt.query.join(PasPtName).filter(
            PasPt.dob == dob,
            PasPtName.firstname.ilike(first_name),
            PasPtName.surname.ilike(last_name)
        ).first()

    if not paspt and not create_new:
        return jsonify({"error": "No matching patient found", "can_create": True}), 404

    if create_new:
        # Create new patient

        paspt = PasPt(
            ur=ur,
            dob=dob,
            gender_code={"male": "M", "female": "F"}.get(patient_info.get("gender", "").lower()),
            race_forrfts_code='1',
            site_id=site_id,
        )
        db.session.add(paspt)
        db.session.commit()

        patient_name = PasPtName(
            patientid=paspt.patientid,
            name_type='legal',
            surname=last_name,
            firstname=first_name
        )
        db.session.add(patient_name)
        db.session.commit()

    if not paspt:
        return jsonify({"error": "Unable to find or create patient"}), 500

    if patient_info.get("test_date"):
        r_session = RSession.query.filter_by(
            patientid=paspt.patientid,
            testdate=patient_info["test_date"]
        ).first()
        if r_session and not overwrite:
            rft_routine = RftRoutine.query.filter_by(sessionid=r_session.sessionid,testtime=patient_info["visit_time"]).all()
            for rft in rft_routine:
                if rft.testtype == "RFTs()":
                    return jsonify({"error": "RFT session already exists for this patient on this date"}), 400
    else:
        # Use current date if no test date is provided
        patient_info["test_date"] = datetime.now().date().isoformat()

    r_session = RSession.query.filter_by(
        patientid=paspt.patientid,
        testdate=patient_info["test_date"]
    ).first()
    if not r_session:
        r_session = RSession(
            patientid=paspt.patientid,
            testdate=patient_info.get("test_date"),
            height=patient_info.get("height"),
            weight=patient_info.get("weight")
        )
        db.session.add(r_session)
        db.session.commit()

    if overwrite:
        # Delete existing RFT records for this session
        existing_rft = RftRoutine.query.filter_by(sessionid=r_session.sessionid).all()
        for rft in existing_rft:
            db.session.delete(rft)
        db.session.commit()
    # Create RFT record
    rft_routine = RftRoutine(
        sessionid=r_session.sessionid,
        patientid=paspt.patientid,
        testtype="RFTs()",
        testtime=patient_info.get("visit_time", datetime.now().time()),
        flowvolloop=base64.b64decode(image_base64),
        r_bl_tlc=measurements.get("tlc", {}).get("pre"),
        r_bl_frc=measurements.get("frc", {}).get("pre"),
        r_bl_rv=measurements.get("rv", {}).get("pre"),
        r_bl_erv=measurements.get("erv", {}).get("pre"),
        r_bl_ic=measurements.get("ic", {}).get("pre"),
        r_bl_vc=measurements.get("vc_max", {}).get("pre"),
        r_bl_fev1=measurements.get("fev_1", {}).get("pre"),
        r_bl_fvc=measurements.get("fvc", {}).get("pre"),
        r_bl_fef2575=measurements.get("fef_25_75", {}).get("pre"),
        r_bl_pef=measurements.get("pef", {}).get("pre"),
        r_bl_tlco=measurements.get("dlco", {}).get("pre"),
        r_bl_va=measurements.get("va", {}).get("pre"),
        r_bl_hb=measurements.get("hb", {}).get("pre"),
        r_bl_ivc=measurements.get("vc_in", {}).get("pre"),
        r_bl_mip=measurements.get("pi_max", {}).get("pre"),
        r_bl_mep=measurements.get("pe_max", {}).get("pre"),
    )
    db.session.add(rft_routine)
    db.session.commit()

    return jsonify({
        "status": "success",
        "patient_id": paspt.patientid,
        "session_id": r_session.sessionid,
        "rft_id": rft_routine.rftid,
    }), 200



@file_bp.route("/api/s3_file_url", methods=["POST"])
def get_presigned_url():
    data = request.get_json()
    s3_key = data.get("key")
    if not s3_key:
        return jsonify({"error": "Missing key"}), 400
    try:
        s3 = boto3.client(
            "s3",
            aws_access_key_id=Config.AWS_ACCESS_KEY,
            aws_secret_access_key=Config.AWS_SECRET_KEY,
            region_name=Config.AWS_REGION_NAME
        )
        bucket = Config.AWS_S3_BUCKET
        url = s3.generate_presigned_url(
            "get_object",
            Params={"Bucket": bucket, "Key": s3_key},
            ExpiresIn=3600
        )
        return jsonify({"url": url})
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@file_bp.route("/api/s3_list_files", methods=["POST"])
def list_s3_files():
    data = request.get_json()
    prefix = data.get("prefix", "")
    try:
        s3 = boto3.client(
            "s3",
            aws_access_key_id=Config.AWS_ACCESS_KEY,
            aws_secret_access_key=Config.AWS_SECRET_KEY,
            region_name=Config.AWS_REGION_NAME
        )

        bucket = Config.AWS_S3_BUCKET
        response = s3.list_objects_v2(Bucket=bucket, Prefix=prefix, Delimiter='/')

        file_keys = [
            obj["Key"] for obj in response.get("Contents", [])
            if obj["Key"] != prefix
        ]

        # Query matching templates by S3 key
        templates = (
            db.session.query(DeviceTemplate)
            .filter(DeviceTemplate.template_s3_key.in_(file_keys))
            .all()
        )
        result = [
            {
                "key": template.template_s3_key,
                "filename": template.filename,
                "manufacturer": template.manufacturer,
                "model": template.device_model,
                "template": template.template_type
            }
            for template in templates
        ]
        return jsonify({"files": result})
    except Exception as e:
        return jsonify({"error": str(e)}), 500





@file_bp.route("/api/pdf_header", methods=["GET"])
def get_pdf_header():
    site_id = request.args.get('site_id', type=int)

    if not site_id:
        return {
            'pdf_picture': None,
            'rows': []
        }, 400  # Bad request if site_id is missing

    # Default fallback image path
    default_image_path = os.path.join(current_app.root_path, 'static/assets/media/logos/logo-23.png')

    # Get the header image for the site
    header_img = SiteSettings.query.filter_by(name='pdf_header_image', site_id=site_id).first()
    image_data = None

    if header_img and header_img.value_json and 'image' in header_img.value_json:
        img = header_img.value_json['image']
        image_data = f"data:{img.get('mimetype')};base64,{img.get('data')}"
    else:
        # Load default image and encode
        try:
            with open(default_image_path, 'rb') as f:
                encoded = base64.b64encode(f.read()).decode('utf-8')
                image_data = f"data:image/png;base64,{encoded}"
        except Exception as e:
            # Fallback if default image is also not found
            image_data = None

    # Get the header text for the site
    header_text = SiteSettings.query.filter_by(name='pdf_header_text', site_id=site_id).first()
    rows = []

    if header_text and header_text.value:
        rows = [line.strip() for line in header_text.value.splitlines() if line.strip()]
    else:
        # Default 4-line fallback text
        rows = [
            "Company: XXXX Pty Ltd",
            "ABN: XXX XXX XXX",
            "Address: 123 Example St, City, State",
            "Email: <EMAIL>"
        ]

    return {
        'pdf_picture': image_data,
        'rows': rows
    }
@file_bp.route("/api/attachments/list", methods=["POST"])
def list_attachments():
    data = request.get_json()
    patient_id = data.get("patient_id")
    rft_id = data.get("rft_id")

    if not patient_id or not rft_id:
        return jsonify({"error": "Missing patient_id or rft_id"}), 400

    try:
        attachments = (
            AttachmentFile.query
            .filter_by(patient_id=patient_id, rft_id=rft_id)
            .order_by(AttachmentFile.uploaded_at.desc())
            .all()
        )

        files = [
            {
                "id": str(file.id),
                "filename": file.filename,
                "s3_key": file.s3_key,
                "uploaded_by": file.uploaded_by,
                "uploaded_at": file.uploaded_at.isoformat(),
                "description": file.description,
            }
            for file in attachments
        ]

        return jsonify({"files": files})
    except Exception as e:
        return jsonify({"error": str(e)}), 500



@file_bp.route("/api/attachments/download", methods=["POST"])
def download_attachment():
    print("!!!")
    data = request.get_json()

    print(data)
    s3_key = data.get("key")
    print("!!!")
    if not s3_key:
        return jsonify({"error": "Missing key"}), 400
    print("!!!")
    try:
        s3 = boto3.client(
            "s3",
            aws_access_key_id=Config.AWS_ACCESS_KEY,
            aws_secret_access_key=Config.AWS_SECRET_KEY,
            region_name=Config.AWS_REGION_NAME
        )

        bucket = Config.AWS_S3_BUCKET

        url = s3.generate_presigned_url(
            "get_object",
            Params={"Bucket": bucket, "Key": s3_key},
            ExpiresIn=3600
        )

        return jsonify({"url": url})
    except Exception as e:
        print("!!!")
        print(e)
        return jsonify({"error": str(e)}), 500

def upload_file_to_s3(file,patient_id,rft_id,site_id,uploaded_by=None,description=None):



    try:
        from werkzeug.utils import secure_filename
        import uuid

        filename = secure_filename(file.filename)
        unique_name = f"{uuid.uuid4().hex}_{filename}"
        s3_key = f"attachments/{unique_name}"
        # Upload to S3
        s3 = boto3.client(
            "s3",
            aws_access_key_id=Config.AWS_ACCESS_KEY,
            aws_secret_access_key=Config.AWS_SECRET_KEY,
            region_name=Config.AWS_REGION_NAME
        )
        s3.upload_fileobj(file, Config.AWS_S3_BUCKET, s3_key)

        # Save to DB
        new_file = AttachmentFile(
            patient_id=int(patient_id),
            site_id=int(site_id),
            rft_id=int(rft_id),
            filename=filename,
            s3_key=s3_key,
            uploaded_by=uploaded_by,
            description=description
        )
        db.session.add(new_file)
        db.session.commit()

        return new_file
    except Exception as e:
        db.session.rollback()
        print(e)
        raise e
@file_bp.route("/api/attachments/upload", methods=["POST"])
def upload_attachment():
    patient_id = request.form.get("patient_id")
    rft_id = request.form.get("rft_id")
    uploaded_by = request.form.get("uploaded_by")  # optional
    description = request.form.get("description")  # optional
    file = request.files.get("file")

    if not all([patient_id, rft_id, file]):
        return jsonify({"error": "Missing required fields"}), 400

    try:
        new_file = upload_file_to_s3(file, patient_id, rft_id, uploaded_by, description)
    except Exception as e:
        print(f"Error uploading file: {e}")

    return jsonify({"success": True, "s3_key": new_file.s3_key, "filename": new_file.filename}), 200
