
from sqlalchemy.orm import relationship
import uuid


from sqlalchemy import Column, Integer, String, Text, Time, TIMESTAMP, ForeignKey, Date, LargeBinary, UUID, func
from app import db

class PasPt(db.Model):
    __tablename__ = 'pas_pt'

    patientid = Column(Integer, primary_key=True)
    ur = Column(String, nullable=False)
    ur_hsid = Column(String, nullable=False)
    dob = Column(Date)
    gender_code = Column(String)
    gender_forrfts_code = Column(String)
    email = Column(String)
    phone_home = Column(String)
    phone_mobile = Column(String)
    phone_work = Column(String)
    countryofbirth_code = Column(String)
    preferredlanguage_code = Column(String)
    aboriginalstatus_code = Column(String)
    medicare_no = Column(String)
    medicare_expirydate = Column(String)
    death_indicator = Column(String)
    death_date = Column(String)
    race_forrfts_code = Column(String)
    site_id = Column(Integer)
    # created = db.Column(db.DateTime, server_default=func.now())
    # updated = db.Column(db.DateTime, server_default=func.now())
    names = relationship("PasPtName", back_populates="patient", cascade="all, delete-orphan")
    addresses = relationship("PasPtAddress", back_populates="patient", cascade="all, delete-orphan")
    ur_numbers = relationship("PasPtUrNumber", back_populates="patient", cascade="all, delete-orphan")


class PasPtName(db.Model):
    __tablename__ = 'pas_pt_names'

    nameid = Column(Integer, primary_key=True)
    patientid = Column(Integer, ForeignKey('pas_pt.patientid'), nullable=False)
    name_type = Column(String, default='primary')
    title = Column(String)
    firstname = Column(String, nullable=False)
    surname = Column(String, nullable=False)
    middlename = Column(String)

    patient = relationship("PasPt", back_populates="names")


class PasPtAddress(db.Model):
    __tablename__ = 'pas_pt_addresses'

    address_id = Column(Integer, primary_key=True)
    patientid = Column(Integer, ForeignKey('pas_pt.patientid'), nullable=False)
    address_type_code = Column(String, default='primary')
    address_1 = Column(String)
    address_2 = Column(String)
    suburb = Column(String)
    postcode = Column(String)

    patient = relationship("PasPt", back_populates="addresses")

class DeviceTemplate(db.Model):
    __tablename__ = 'device_templates'
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    filename = Column(Text, nullable=False)
    template_s3_key = Column(Text, nullable=False, unique=True)
    instruction_s3_key = Column(Text, nullable=True)
    manufacturer = Column(String, nullable=False)
    device_model = Column(String, nullable=False)
    template_type = Column(String, nullable=False)
class PasPtUrNumber(db.Model):
    __tablename__ = 'pas_pt_ur_numbers'

    ur_id = Column(Integer, primary_key=True)
    patientid = Column(Integer, ForeignKey('pas_pt.patientid'), nullable=False)
    ur = Column(String, nullable=False)
    ur_hsid = Column(String, nullable=False)
    ur_status = Column(String, nullable=False)

    patient = relationship("PasPt", back_populates="ur_numbers")



class AttachmentFile(db.Model):
    __tablename__ = 'attachment_files'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    site_id = Column(Integer, nullable=False)
    patient_id = Column(Integer, nullable=True)
    rft_id = Column(Integer, nullable=True)
    filename = Column(Text, nullable=False)
    s3_key = Column(Text, nullable=False, unique=True)
    uploaded_by = Column(Text, nullable=True)
    uploaded_at = Column(TIMESTAMP(timezone=False), server_default=func.now())
    description = Column(Text, nullable=True)


class RSession(db.Model):
    __tablename__ = 'r_sessions'

    sessionid = Column(Integer, primary_key=True)
    patientid = Column(Integer, ForeignKey('pas_pt.patientid'), nullable=True)

    testdate = Column(Date)
    lab = Column(String)
    height = Column(String)
    weight = Column(String)

    req_name = Column(String)
    req_address = Column(String)
    req_providernumber = Column(String)
    req_healthservice_text = Column(String)
    req_healthservice_code = Column(String)

    req_date = Column(Date)
    req_time = Column(Time)
    req_phone = Column(String)
    req_fax = Column(String)
    req_email = Column(String)
    req_clinicalnotes = Column(Text)

    smoke_hx = Column(String)
    smoke_cigsperday = Column(String)
    smoke_yearssmoked = Column(String)
    smoke_packyears = Column(String)
    smoke_last = Column(String)

    diagnosticcategory = Column(String)
    pred_sourceids = Column(Text)

    admissionstatus = Column(String)
    report_copyto = Column(String)
    report_copyto_2 = Column(String)

    billing_billingmo = Column(String)
    billing_billingmoproviderno = Column(String)

    lastupdated_session = Column(TIMESTAMP)
    lastupdatedby_session = Column(String)

    # --- Relationships ---
    patient = relationship("PasPt", backref="sessions")



class RftRoutine(db.Model):
    __tablename__ = 'rft_routine'

    rftid = Column(Integer, primary_key=True)
    sessionid = Column(Integer, ForeignKey('r_sessions.sessionid'))
    patientid = Column(Integer, ForeignKey('pas_pt.patientid'))

    testtime = Column(Time)
    testtype = Column(String)
    lab = Column(String)

    report_text = Column(Text)
    report_reportedby = Column(String)
    report_reporteddate = Column(TIMESTAMP)
    report_verifiedby = Column(String)
    report_verifieddate = Column(TIMESTAMP)
    report_authorisedby = Column(String)
    report_authoriseddate = Column(TIMESTAMP)
    report_amendedby = Column(String)
    report_amendeddate = Column(TIMESTAMP)
    report_amendednotes = Column(Text)
    report_status = Column(String)

    # Baseline
    r_bl_fev1 = Column(String)
    r_bl_fvc = Column(String)
    r_bl_vc = Column(String)
    r_bl_fer = Column(String)
    r_bl_fef2575 = Column(String)
    r_bl_pef = Column(String)
    r_pre_condition = Column(String)
    r_bl_tlco = Column(String)
    r_bl_kco = Column(String)
    r_bl_va = Column(String)
    r_bl_hb = Column(String)
    r_bl_ivc = Column(String)
    r_bl_tlc = Column(String)
    r_bl_frc = Column(String)
    r_bl_rv = Column(String)
    r_bl_erv = Column(String)
    r_bl_ic = Column(String)
    r_bl_rvtlc = Column(String)
    r_bl_lvvc = Column(String)
    r_bl_mip = Column(String)
    r_bl_mep = Column(String)
    r_bl_snip = Column(String)
    r_spo2_1 = Column(String)
    r_spo2_2 = Column(String)
    r_bl_feno = Column(String)

    # Post
    r_post_fev1 = Column(String)
    r_post_fvc = Column(String)
    r_post_vc = Column(String)
    r_post_fer = Column(String)
    r_post_fef2575 = Column(String)
    r_post_pef = Column(String)
    r_post_condition = Column(String)

    # Conditions
    r_condition_tl = Column(String)
    r_condition_lv = Column(String)
    r_condition_mrp = Column(String)
    r_condition_feno = Column(String)
    flowvolloop = Column(LargeBinary)  # Base64 encoded image
    # Flow volume & BD
    # bdstatus = Column(String)
    # technicalnotes = Column(Text)
    # scientist = Column(String)
    #
    # # ABG1
    # r_abg1_fio2 = Column(String)
    # r_abg1_ph = Column(String)
    # r_abg1_pao2 = Column(String)
    # r_abg1_paco2 = Column(String)
    # r_abg1_sao2 = Column(String)
    # r_abg1_cohb = Column(String)
    # r_abg1_be = Column(String)
    # r_abg1_hco3 = Column(String)
    # r_abg1_aapo2 = Column(String)
    # r_abg1_shunt = Column(String)
    #
    # # ABG2
    # r_abg2_fio2 = Column(String)
    # r_abg2_ph = Column(String)
    # r_abg2_pao2 = Column(String)
    # r_abg2_paco2 = Column(String)
    # r_abg2_sao2 = Column(String)
    # r_abg2_cohb = Column(String)
    # r_abg2_be = Column(String)
    # r_abg2_hco3 = Column(String)
    # r_abg2_aapo2 = Column(String)
    # r_abg2_shunt = Column(String)
    #
    # # Additional
    # lungvolumes_method = Column(String)
    # lastupdated_rft = Column(TIMESTAMP)
    # lastupdatedby_rft = Column(String)
    # sample_type = Column(String)
    # r_abg1_sampletype = Column(String)
    # r_abg2_sampletype = Column(String)
    # device_info = Column(Text)

    # --- Relationships ---
    session = db.relationship("RSession", backref="rft_routine")
    patient = db.relationship("PasPt", backref="rft_routine")

