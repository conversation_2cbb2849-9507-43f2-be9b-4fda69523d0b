"""Create Flask application."""
from apifairy import APIFairy
from flask import Flask
from flask import redirect, url_for
from flask_admin import AdminIndexView
from flask_bootstrap import Bootstrap4
from flask_jwt_extended import J<PERSON>TManager
from flask_login import <PERSON>gin<PERSON>anager, current_user
from flask_marshmallow import Marshmallow
from flask_migrate import Migrate
from flask_principal import Principal, identity_loaded
from flask_security import Security, SQLAlchemyUserDatastore
from flask_security.models import fsqla
from flask_sqlalchemy import SQLAlchemy
from flask_talisman import Talisman
from flask_wtf import CSRFProtect
from flask_babel import Babel
from sqlalchemy import MetaData
from sqlalchemy.orm import DeclarativeBase
from sqlalchemy_utils import PhoneNumber

from admin_categories import RezibaseAdmin, add_head_categories
from app.sentry_init import SentryMessage
from common import AdminSecurityMixin, mail
from config import Config
from flask_principal_permissions import load_identity

convention = {
    "ix": 'ix_%(column_0_label)s',
    "uq": "uq_%(table_name)s_%(column_0_name)s",
    "ck": "ck_%(table_name)s_%(constraint_name)s",
    "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
    "pk": "pk_%(table_name)s"
}

sqlalchemy_metadata = MetaData(naming_convention=convention)

class Base(DeclarativeBase):
    metadata = sqlalchemy_metadata


db = SQLAlchemy(model_class=Base)
security = Security()
login_manager = LoginManager()
migrate = Migrate()
principals = Principal()
csrf = CSRFProtect()
jwt = JWTManager()
ma = Marshmallow()
apifairy = APIFairy()
bootstrap = Bootstrap4()
admin = RezibaseAdmin(
    name="Rezibase", template_mode='bootstrap3', static_url_path=None,
    base_template=f'admin/site/default/base.html'
)


class ReziDash(AdminSecurityMixin, AdminIndexView):
    def is_accessible(self):
        # Check if the user is authenticated and has the required role
        return current_user.is_authenticated and current_user.has_role('superuser')

    def inaccessible_callback(self, name, **kwargs):
        return redirect(url_for('auth_bp.login_view'))

def init_app(config_class=Config):
    app = Flask(__name__, instance_relative_config=False)
    app.config.from_object(config_class)
    babel = Babel(app)
    db.init_app(app)
    ma.init_app(app)
    login_manager.init_app(app)
    fsqla.FsModels.set_db_info(db, user_table_name="ab_user")
    migrate.init_app(app, db)
    principals.init_app(app)
    csrf.init_app(app)
    apifairy.init_app(app)
    bootstrap.init_app(app)
    mail.init_app(app)
    jwt.init_app(app)

    admin.init_app(app, index_view=ReziDash())
    add_head_categories(admin)  # add head categories for flask admin
    from json import JSONEncoder

    class CustomJSONEncoder(JSONEncoder):
        def default(self, obj):
            if isinstance(obj, PhoneNumber):
                return obj.national_number

            return JSONEncoder.default(self, obj)  # aka super()

    app.json_encoder = CustomJSONEncoder  # Flask app
    SentryMessage()
    with app.app_context():
        from app import auth
        from app.home import home
        from app.utils import utils
        from app.dashboard import dashboard
        from app.request_events import register_request_events
        from app.user_events import register_user_events
        from app.auth.models import User, Role
        from app.dashboard import add_admins as add_dashboard_admins
        from app import qc
        from app.pdf_convert import pdf_convert
        from app.activity_logs import init_app as init_activity_logs
        import app.qc.alarms_api as alarms_api

        # Register Blueprints
        app.register_blueprint(auth.auth_bp)
        app.register_blueprint(pdf_convert.file_bp)
        app.register_blueprint(utils.utils_bp)
        app.register_blueprint(home.home_bp)
        app.register_blueprint(dashboard.dashboard_bp)
        app.register_blueprint(qc.views.qc_bp)
        app.register_blueprint(alarms_api.alerts_bp)
        user_datastore = SQLAlchemyUserDatastore(db, User, Role)
        security.init_app(app, user_datastore)
        auth.add_admins(admin)
        add_dashboard_admins(admin)
        utils.add_admins(admin)
        qc.admin.add_admins(admin)
        register_request_events(app)
        register_user_events(app)
        init_activity_logs(app)
        Talisman(app, content_security_policy=None, force_https=False)
        models = {
            mapper.class_.__name__: mapper.class_
            for mapper in db.Model.registry.mappers
        }

        @identity_loaded.connect_via(app)
        def on_identity_loaded(sender, identity):
            load_identity(sender, identity, current_user)

    return app

