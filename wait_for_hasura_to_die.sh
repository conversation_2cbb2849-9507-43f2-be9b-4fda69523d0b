#!/bin/bash
set -euo pipefail

# Get the absolute path of the directory where this script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
GRAPHQL_PID="$1"

if [ -n "$GRAPHQL_PID" ] && [[ "$GRAPHQL_PID" =~ ^[0-9]+$ ]] && kill -0 "$GRAPHQL_PID" 2>/dev/null; then
  echo "INFO (cleanup): GraphQL Engine (PID: $GRAPHQL_PID) is running. Attempting shutdown..."
  kill "$GRAPHQL_PID" # Send SIGTERM
  
  GRACEFUL_SHUTDOWN_SECONDS=25 
  SECONDS_WAITED_SHUTDOWN=0
  while kill -0 "$GRAPHQL_PID" 2>/dev/null; do
    if [ "$SECONDS_WAITED_SHUTDOWN" -ge "$GRACEFUL_SHUTDOWN_SECONDS" ]; then
      echo "WARN (cleanup): GraphQL Engine (PID: $GRAPHQL_PID) did not terminate gracefully after ${GRACEFUL_SHUTDOWN_SECONDS}s. Sending SIGKILL..."
      kill -9 "$GRAPHQL_PID" 2>/dev/null
      break
    fi
    sleep 1
    SECONDS_WAITED_SHUTDOWN=$((SECONDS_WAITED_SHUTDOWN + 1))
  done

  if kill -0 "$GRAPHQL_PID" 2>/dev/null; then
      echo "ERROR (cleanup): Failed to stop GraphQL Engine (PID: $GRAPHQL_PID) even with SIGKILL."
  else
      echo "INFO (cleanup): GraphQL Engine (PID: $GRAPHQL_PID) has been shut down."
  fi
  wait "$GRAPHQL_PID" 2>/dev/null || true # Reap the process, suppress error if already gone
elif [ -n "$GRAPHQL_PID" ] && [[ "$GRAPHQL_PID" =~ ^[0-9]+$ ]]; then
  # PID was set, but process is not running when cleanup is called
  echo "INFO (cleanup): GraphQL Engine (PID: $GRAPHQL_PID) was not running or already stopped."
fi
